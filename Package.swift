// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "VoiceRecorderWatch",
    platforms: [
        .watchOS(.v10)
    ],
    products: [
        .executable(
            name: "VoiceRecorderWatch",
            targets: ["VoiceRecorderWatch"]
        )
    ],
    targets: [
        .executableTarget(
            name: "VoiceRecorderWatch",
            path: ".",
            sources: ["VoiceRecorderWatchApp.swift"]
        )
    ]
)
