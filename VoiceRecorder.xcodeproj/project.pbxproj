// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789012A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012B /* AppDelegate.m */; };
		A1234567890123456789012C /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012D /* ViewController.m */; };
		A1234567890123456789012E /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012F /* Main.storyboard */; };
		A1234567890123456789013A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013B /* Assets.xcassets */; };
		A1234567890123456789013C /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013D /* LaunchScreen.storyboard */; };
		A1234567890123456789013E /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013F /* main.m */; };
		A1234567890123456789014A /* VoiceRecorder WatchKit App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = A1234567890123456789014B /* VoiceRecorder WatchKit App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		A1234567890123456789014C /* Interface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789014D /* Interface.storyboard */; };
		A1234567890123456789014E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789014F /* Assets.xcassets */; };
		A1234567890123456789015A /* VoiceRecorder WatchKit Extension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = A1234567890123456789015B /* VoiceRecorder WatchKit Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		A1234567890123456789015C /* InterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789015D /* InterfaceController.m */; };
		A1234567890123456789015E /* ExtensionDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789015F /* ExtensionDelegate.m */; };
		A1234567890123456789016A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789016B /* Assets.xcassets */; };
		A1234567890123456789016C /* RecordingManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789016D /* RecordingManager.m */; };
		A123456789012345678901B7 /* RecordingListInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901B8 /* RecordingListInterfaceController.m */; };
		A123456789012345678901B9 /* RecordingRowController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901BA /* RecordingRowController.m */; };
		A123456789012345678901BB /* RecordingDetailInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901BC /* RecordingDetailInterfaceController.m */; };
		A123456789012345678901C6 /* SMSMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901C7 /* SMSMessage.m */; };
		A123456789012345678901C8 /* SMSDataManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901C9 /* SMSDataManager.m */; };
		A123456789012345678901CA /* SMSNotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901CB /* SMSNotificationService.m */; };
		A123456789012345678901CC /* SMSListInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901CD /* SMSListInterfaceController.m */; };
		A123456789012345678901CE /* SMSRowController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901CF /* SMSRowController.m */; };
		A123456789012345678901D0 /* SMSDetailInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A123456789012345678901D1 /* SMSDetailInterfaceController.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A1234567890123456789017A = {
			isa = PBXContainerItemProxy;
			containerPortal = A1234567890123456789017B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A1234567890123456789017C;
			remoteInfo = "VoiceRecorder WatchKit App";
		};
		A1234567890123456789017D = {
			isa = PBXContainerItemProxy;
			containerPortal = A1234567890123456789017B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A1234567890123456789017E;
			remoteInfo = "VoiceRecorder WatchKit Extension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A1234567890123456789018A = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				A1234567890123456789014A /* VoiceRecorder WatchKit App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1234567890123456789018B = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				A1234567890123456789015A /* VoiceRecorder WatchKit Extension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		A1234567890123456789019A /* VoiceRecorder.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VoiceRecorder.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789012B /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		A1234567890123456789019B /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		A1234567890123456789012D /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		A1234567890123456789019C /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		A1234567890123456789012F /* Main.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Main.storyboard; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		A1234567890123456789013B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789013D /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		A1234567890123456789019D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789013F /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		A1234567890123456789014B /* VoiceRecorder WatchKit App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "VoiceRecorder WatchKit App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789014D /* Interface.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Interface.storyboard; path = Base.lproj/Interface.storyboard; sourceTree = "<group>"; };
		A1234567890123456789014F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789019E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789015B /* VoiceRecorder WatchKit Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "VoiceRecorder WatchKit Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789015D /* InterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InterfaceController.m; sourceTree = "<group>"; };
		A1234567890123456789019F /* InterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InterfaceController.h; sourceTree = "<group>"; };
		A1234567890123456789015F /* ExtensionDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ExtensionDelegate.m; sourceTree = "<group>"; };
		A123456789012345678901A0 /* ExtensionDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExtensionDelegate.h; sourceTree = "<group>"; };
		A1234567890123456789016B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A123456789012345678901A1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789016D /* RecordingManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordingManager.m; sourceTree = "<group>"; };
		A123456789012345678901A2 /* RecordingManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordingManager.h; sourceTree = "<group>"; };
		A123456789012345678901B8 /* RecordingListInterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordingListInterfaceController.m; sourceTree = "<group>"; };
		A123456789012345678901BD /* RecordingListInterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordingListInterfaceController.h; sourceTree = "<group>"; };
		A123456789012345678901BA /* RecordingRowController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordingRowController.m; sourceTree = "<group>"; };
		A123456789012345678901BE /* RecordingRowController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordingRowController.h; sourceTree = "<group>"; };
		A123456789012345678901BC /* RecordingDetailInterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordingDetailInterfaceController.m; sourceTree = "<group>"; };
		A123456789012345678901BF /* RecordingDetailInterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordingDetailInterfaceController.h; sourceTree = "<group>"; };
		A123456789012345678901C7 /* SMSMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSMessage.m; sourceTree = "<group>"; };
		A123456789012345678901D2 /* SMSMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSMessage.h; sourceTree = "<group>"; };
		A123456789012345678901C9 /* SMSDataManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSDataManager.m; sourceTree = "<group>"; };
		A123456789012345678901D3 /* SMSDataManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSDataManager.h; sourceTree = "<group>"; };
		A123456789012345678901CB /* SMSNotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSNotificationService.m; sourceTree = "<group>"; };
		A123456789012345678901D4 /* SMSNotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSNotificationService.h; sourceTree = "<group>"; };
		A123456789012345678901CD /* SMSListInterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSListInterfaceController.m; sourceTree = "<group>"; };
		A123456789012345678901D5 /* SMSListInterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSListInterfaceController.h; sourceTree = "<group>"; };
		A123456789012345678901CF /* SMSRowController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSRowController.m; sourceTree = "<group>"; };
		A123456789012345678901D6 /* SMSRowController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSRowController.h; sourceTree = "<group>"; };
		A123456789012345678901D1 /* SMSDetailInterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSDetailInterfaceController.m; sourceTree = "<group>"; };
		A123456789012345678901D7 /* SMSDetailInterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSDetailInterfaceController.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A123456789012345678901A3 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A123456789012345678901A4 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A123456789012345678901A5 = {
			isa = PBXGroup;
			children = (
				A123456789012345678901A6 /* VoiceRecorder */,
				A123456789012345678901A7 /* VoiceRecorder WatchKit App */,
				A123456789012345678901A8 /* VoiceRecorder WatchKit Extension */,
				A123456789012345678901A9 /* Products */,
			);
			sourceTree = "<group>";
		};
		A123456789012345678901A6 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789019B /* AppDelegate.h */,
				A1234567890123456789012B /* AppDelegate.m */,
				A1234567890123456789019C /* ViewController.h */,
				A1234567890123456789012D /* ViewController.m */,
				A1234567890123456789012F /* Main.storyboard */,
				A1234567890123456789013B /* Assets.xcassets */,
				A1234567890123456789013D /* LaunchScreen.storyboard */,
				A1234567890123456789019D /* Info.plist */,
				A123456789012345678901AA /* Supporting Files */,
			);
			path = VoiceRecorder;
			sourceTree = "<group>";
		};
		A123456789012345678901AA = {
			isa = PBXGroup;
			children = (
				A1234567890123456789013F /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		A123456789012345678901A7 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789014D /* Interface.storyboard */,
				A1234567890123456789014F /* Assets.xcassets */,
				A1234567890123456789019E /* Info.plist */,
			);
			path = "VoiceRecorder WatchKit App";
			sourceTree = "<group>";
		};
		A123456789012345678901A8 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789019F /* InterfaceController.h */,
				A1234567890123456789015D /* InterfaceController.m */,
				A123456789012345678901A0 /* ExtensionDelegate.h */,
				A1234567890123456789015F /* ExtensionDelegate.m */,
				A123456789012345678901A2 /* RecordingManager.h */,
				A1234567890123456789016D /* RecordingManager.m */,
				A123456789012345678901BD /* RecordingListInterfaceController.h */,
				A123456789012345678901B8 /* RecordingListInterfaceController.m */,
				A123456789012345678901BE /* RecordingRowController.h */,
				A123456789012345678901BA /* RecordingRowController.m */,
				A123456789012345678901BF /* RecordingDetailInterfaceController.h */,
				A123456789012345678901BC /* RecordingDetailInterfaceController.m */,
				A123456789012345678901D2 /* SMSMessage.h */,
				A123456789012345678901C7 /* SMSMessage.m */,
				A123456789012345678901D3 /* SMSDataManager.h */,
				A123456789012345678901C9 /* SMSDataManager.m */,
				A123456789012345678901D4 /* SMSNotificationService.h */,
				A123456789012345678901CB /* SMSNotificationService.m */,
				A123456789012345678901D5 /* SMSListInterfaceController.h */,
				A123456789012345678901CD /* SMSListInterfaceController.m */,
				A123456789012345678901D6 /* SMSRowController.h */,
				A123456789012345678901CF /* SMSRowController.m */,
				A123456789012345678901D7 /* SMSDetailInterfaceController.h */,
				A123456789012345678901D1 /* SMSDetailInterfaceController.m */,
				A1234567890123456789016B /* Assets.xcassets */,
				A123456789012345678901A1 /* Info.plist */,
			);
			path = "VoiceRecorder WatchKit Extension";
			sourceTree = "<group>";
		};
		A123456789012345678901A9 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789019A /* VoiceRecorder.app */,
				A1234567890123456789014B /* VoiceRecorder WatchKit App.app */,
				A1234567890123456789015B /* VoiceRecorder WatchKit Extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456789017C = {
			isa = PBXNativeTarget;
			buildConfigurationList = A123456789012345678901AB /* Build configuration list for PBXNativeTarget "VoiceRecorder" */;
			buildPhases = (
				A123456789012345678901A3 /* Sources */,
				A123456789012345678901AC /* Frameworks */,
				A123456789012345678901AD /* Resources */,
				A1234567890123456789018A /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				A1234567890123456789017A /* PBXTargetDependency */,
			);
			name = VoiceRecorder;
			productName = VoiceRecorder;
			productReference = A1234567890123456789019A /* VoiceRecorder.app */;
			productType = "com.apple.product-type.application";
		};
		A1234567890123456789017E = {
			isa = PBXNativeTarget;
			buildConfigurationList = A123456789012345678901AE /* Build configuration list for PBXNativeTarget "VoiceRecorder WatchKit App" */;
			buildPhases = (
				A123456789012345678901AF /* Resources */,
				A1234567890123456789018B /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				A1234567890123456789017D /* PBXTargetDependency */,
			);
			name = "VoiceRecorder WatchKit App";
			productName = "VoiceRecorder WatchKit App";
			productReference = A1234567890123456789014B /* VoiceRecorder WatchKit App.app */;
			productType = "com.apple.product-type.application.watchapp2";
		};
		A123456789012345678901B0 = {
			isa = PBXNativeTarget;
			buildConfigurationList = A123456789012345678901B1 /* Build configuration list for PBXNativeTarget "VoiceRecorder WatchKit Extension" */;
			buildPhases = (
				A123456789012345678901B2 /* Sources */,
				A123456789012345678901A4 /* Frameworks */,
				A123456789012345678901B3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "VoiceRecorder WatchKit Extension";
			productName = "VoiceRecorder WatchKit Extension";
			productReference = A1234567890123456789015B /* VoiceRecorder WatchKit Extension.appex */;
			productType = "com.apple.product-type.watchkit2-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456789017B = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890123456789017C = {
						CreatedOnToolsVersion = 15.0;
					};
					A1234567890123456789017E = {
						CreatedOnToolsVersion = 15.0;
					};
					A123456789012345678901B0 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A123456789012345678901B4 /* Build configuration list for PBXProject "VoiceRecorder" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A123456789012345678901A5;
			productRefGroup = A123456789012345678901A9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456789017C /* VoiceRecorder */,
				A1234567890123456789017E /* VoiceRecorder WatchKit App */,
				A123456789012345678901B0 /* VoiceRecorder WatchKit Extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A123456789012345678901AD = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789013C /* LaunchScreen.storyboard in Resources */,
				A1234567890123456789013A /* Assets.xcassets in Resources */,
				A1234567890123456789012E /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A123456789012345678901AF = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789014E /* Assets.xcassets in Resources */,
				A1234567890123456789014C /* Interface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A123456789012345678901B3 = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789016A /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A123456789012345678901A3 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789012C /* ViewController.m in Sources */,
				A1234567890123456789013E /* main.m in Sources */,
				A1234567890123456789012A /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A123456789012345678901B2 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789016C /* RecordingManager.m in Sources */,
				A1234567890123456789015E /* ExtensionDelegate.m in Sources */,
				A1234567890123456789015C /* InterfaceController.m in Sources */,
				A123456789012345678901B7 /* RecordingListInterfaceController.m in Sources */,
				A123456789012345678901B9 /* RecordingRowController.m in Sources */,
				A123456789012345678901BB /* RecordingDetailInterfaceController.m in Sources */,
				A123456789012345678901C6 /* SMSMessage.m in Sources */,
				A123456789012345678901C8 /* SMSDataManager.m in Sources */,
				A123456789012345678901CA /* SMSNotificationService.m in Sources */,
				A123456789012345678901CC /* SMSListInterfaceController.m in Sources */,
				A123456789012345678901CE /* SMSRowController.m in Sources */,
				A123456789012345678901D0 /* SMSDetailInterfaceController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A1234567890123456789017A = {
			isa = PBXTargetDependency;
			target = A1234567890123456789017E /* VoiceRecorder WatchKit App */;
			targetProxy = A1234567890123456789017A /* PBXContainerItemProxy */;
		};
		A1234567890123456789017D = {
			isa = PBXTargetDependency;
			target = A123456789012345678901B0 /* VoiceRecorder WatchKit Extension */;
			targetProxy = A1234567890123456789017D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A123456789012345678901B5 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		A123456789012345678901B6 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A123456789012345678901C0 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VoiceRecorder/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A123456789012345678901C1 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VoiceRecorder/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A123456789012345678901C2 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				IBSC_MODULE = VoiceRecorder_WatchKit_Extension;
				INFOPLIST_FILE = "VoiceRecorder WatchKit App/Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		A123456789012345678901C3 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				IBSC_MODULE = VoiceRecorder_WatchKit_Extension;
				INFOPLIST_FILE = "VoiceRecorder WatchKit App/Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		A123456789012345678901C4 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "VoiceRecorder WatchKit Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp.watchkitextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		A123456789012345678901C5 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "VoiceRecorder WatchKit Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp.watchkitextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A123456789012345678901B4 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A123456789012345678901B5 /* Debug */,
				A123456789012345678901B6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A123456789012345678901AB = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A123456789012345678901C0 /* Debug */,
				A123456789012345678901C1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A123456789012345678901AE = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A123456789012345678901C2 /* Debug */,
				A123456789012345678901C3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A123456789012345678901B1 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A123456789012345678901C4 /* Debug */,
				A123456789012345678901C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1234567890123456789017B /* Project object */;
}
