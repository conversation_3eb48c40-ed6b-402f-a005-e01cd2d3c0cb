// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1001 /* InterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = A1002 /* InterfaceController.m */; };
		A1003 /* ExtensionDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = A1004 /* ExtensionDelegate.m */; };
		A1005 /* RecordingManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A1006 /* RecordingManager.m */; };
		A1007 /* SMSMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = A1008 /* SMSMessage.m */; };
		A1009 /* SMSDataManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A1010 /* SMSDataManager.m */; };
		A1011 /* SMSNotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = A1012 /* SMSNotificationService.m */; };
		A1013 /* Interface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A1014 /* Interface.storyboard */; };
		A1015 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1016 /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A2001 /* VoiceRecorder WatchKit Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "VoiceRecorder WatchKit Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1002 /* InterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InterfaceController.m; sourceTree = "<group>"; };
		A2002 /* InterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InterfaceController.h; sourceTree = "<group>"; };
		A1004 /* ExtensionDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ExtensionDelegate.m; sourceTree = "<group>"; };
		A2003 /* ExtensionDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExtensionDelegate.h; sourceTree = "<group>"; };
		A1006 /* RecordingManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordingManager.m; sourceTree = "<group>"; };
		A2004 /* RecordingManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordingManager.h; sourceTree = "<group>"; };
		A1008 /* SMSMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSMessage.m; sourceTree = "<group>"; };
		A2005 /* SMSMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSMessage.h; sourceTree = "<group>"; };
		A1010 /* SMSDataManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSDataManager.m; sourceTree = "<group>"; };
		A2006 /* SMSDataManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSDataManager.h; sourceTree = "<group>"; };
		A1012 /* SMSNotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SMSNotificationService.m; sourceTree = "<group>"; };
		A2007 /* SMSNotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SMSNotificationService.h; sourceTree = "<group>"; };
		A1014 /* Interface.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Interface.storyboard; path = Base.lproj/Interface.storyboard; sourceTree = "<group>"; };
		A1016 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A2008 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A3001 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A4001 = {
			isa = PBXGroup;
			children = (
				A4002 /* VoiceRecorder WatchKit Extension */,
				A4003 /* VoiceRecorder WatchKit App */,
				A4004 /* Products */,
			);
			sourceTree = "<group>";
		};
		A4002 = {
			isa = PBXGroup;
			children = (
				A2002 /* InterfaceController.h */,
				A1002 /* InterfaceController.m */,
				A2003 /* ExtensionDelegate.h */,
				A1004 /* ExtensionDelegate.m */,
				A2004 /* RecordingManager.h */,
				A1006 /* RecordingManager.m */,
				A2005 /* SMSMessage.h */,
				A1008 /* SMSMessage.m */,
				A2006 /* SMSDataManager.h */,
				A1010 /* SMSDataManager.m */,
				A2007 /* SMSNotificationService.h */,
				A1012 /* SMSNotificationService.m */,
				A1016 /* Assets.xcassets */,
				A2008 /* Info.plist */,
			);
			path = "VoiceRecorder WatchKit Extension";
			sourceTree = "<group>";
		};
		A4003 = {
			isa = PBXGroup;
			children = (
				A1014 /* Interface.storyboard */,
			);
			path = "VoiceRecorder WatchKit App";
			sourceTree = "<group>";
		};
		A4004 = {
			isa = PBXGroup;
			children = (
				A2001 /* VoiceRecorder WatchKit Extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A5001 = {
			isa = PBXNativeTarget;
			buildConfigurationList = A6001 /* Build configuration list for PBXNativeTarget "VoiceRecorder WatchKit Extension" */;
			buildPhases = (
				A7001 /* Sources */,
				A3001 /* Frameworks */,
				A8001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "VoiceRecorder WatchKit Extension";
			productName = "VoiceRecorder WatchKit Extension";
			productReference = A2001 /* VoiceRecorder WatchKit Extension.appex */;
			productType = "com.apple.product-type.watchkit2-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A9001 = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					A5001 = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = A6002 /* Build configuration list for PBXProject "VoiceRecorder" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A4001;
			productRefGroup = A4004 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A5001 /* VoiceRecorder WatchKit Extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A8001 = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1015 /* Assets.xcassets in Resources */,
				A1013 /* Interface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A7001 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1001 /* InterfaceController.m in Sources */,
				A1003 /* ExtensionDelegate.m in Sources */,
				A1005 /* RecordingManager.m in Sources */,
				A1007 /* SMSMessage.m in Sources */,
				A1009 /* SMSDataManager.m in Sources */,
				A1011 /* SMSNotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A10001 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = watchos;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		A10002 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = watchos;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		A10003 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGNING_REQUIRED = NO;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "VoiceRecorder WatchKit Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp.watchkitextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		A10004 = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGNING_REQUIRED = NO;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "VoiceRecorder WatchKit Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.VoiceRecorder.watchkitapp.watchkitextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A6002 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A10001 /* Debug */,
				A10002 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A6001 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A10003 /* Debug */,
				A10004 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A9001 /* Project object */;
}