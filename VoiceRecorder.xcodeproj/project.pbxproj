// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		F3075F952E06960800518A37 /* VoiceRecorder Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = F3075F942E06960800518A37 /* VoiceRecorder Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F3075F962E06960800518A37 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3075F882E06960800518A37 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3075F932E06960800518A37;
			remoteInfo = "VoiceRecorder Watch App";
		};
		F3075FA42E06960A00518A37 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3075F882E06960800518A37 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3075F932E06960800518A37;
			remoteInfo = "VoiceRecorder Watch App";
		};
		F3075FAE2E06960A00518A37 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3075F882E06960800518A37 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3075F932E06960800518A37;
			remoteInfo = "VoiceRecorder Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		F3075FBA2E06960A00518A37 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				F3075F952E06960800518A37 /* VoiceRecorder Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		F3075F8E2E06960800518A37 /* VoiceRecorder.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VoiceRecorder.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3075F942E06960800518A37 /* VoiceRecorder Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "VoiceRecorder Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		F3075FA32E06960A00518A37 /* VoiceRecorder Watch AppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "VoiceRecorder Watch AppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		F3075FAD2E06960A00518A37 /* VoiceRecorder Watch AppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "VoiceRecorder Watch AppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F3075F982E06960800518A37 /* VoiceRecorder Watch App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "VoiceRecorder Watch App";
			sourceTree = "<group>";
		};
		F3075FA62E06960A00518A37 /* VoiceRecorder Watch AppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "VoiceRecorder Watch AppTests";
			sourceTree = "<group>";
		};
		F3075FB02E06960A00518A37 /* VoiceRecorder Watch AppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "VoiceRecorder Watch AppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F3075F912E06960800518A37 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075FA02E06960A00518A37 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075FAA2E06960A00518A37 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F3075F872E06960800518A37 = {
			isa = PBXGroup;
			children = (
				F3075F982E06960800518A37 /* VoiceRecorder Watch App */,
				F3075FA62E06960A00518A37 /* VoiceRecorder Watch AppTests */,
				F3075FB02E06960A00518A37 /* VoiceRecorder Watch AppUITests */,
				F3075F8F2E06960800518A37 /* Products */,
			);
			sourceTree = "<group>";
		};
		F3075F8F2E06960800518A37 /* Products */ = {
			isa = PBXGroup;
			children = (
				F3075F8E2E06960800518A37 /* VoiceRecorder.app */,
				F3075F942E06960800518A37 /* VoiceRecorder Watch App.app */,
				F3075FA32E06960A00518A37 /* VoiceRecorder Watch AppTests.xctest */,
				F3075FAD2E06960A00518A37 /* VoiceRecorder Watch AppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F3075F8D2E06960800518A37 /* VoiceRecorder */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3075FBB2E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder" */;
			buildPhases = (
				F3075F8C2E06960800518A37 /* Resources */,
				F3075FBA2E06960A00518A37 /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				F3075F972E06960800518A37 /* PBXTargetDependency */,
			);
			name = VoiceRecorder;
			packageProductDependencies = (
			);
			productName = VoiceRecorder;
			productReference = F3075F8E2E06960800518A37 /* VoiceRecorder.app */;
			productType = "com.apple.product-type.application.watchapp2-container";
		};
		F3075F932E06960800518A37 /* VoiceRecorder Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3075FB72E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch App" */;
			buildPhases = (
				F3075F902E06960800518A37 /* Sources */,
				F3075F912E06960800518A37 /* Frameworks */,
				F3075F922E06960800518A37 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F3075F982E06960800518A37 /* VoiceRecorder Watch App */,
			);
			name = "VoiceRecorder Watch App";
			packageProductDependencies = (
			);
			productName = "VoiceRecorder Watch App";
			productReference = F3075F942E06960800518A37 /* VoiceRecorder Watch App.app */;
			productType = "com.apple.product-type.application";
		};
		F3075FA22E06960A00518A37 /* VoiceRecorder Watch AppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3075FBE2E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch AppTests" */;
			buildPhases = (
				F3075F9F2E06960A00518A37 /* Sources */,
				F3075FA02E06960A00518A37 /* Frameworks */,
				F3075FA12E06960A00518A37 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F3075FA52E06960A00518A37 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F3075FA62E06960A00518A37 /* VoiceRecorder Watch AppTests */,
			);
			name = "VoiceRecorder Watch AppTests";
			packageProductDependencies = (
			);
			productName = "VoiceRecorder Watch AppTests";
			productReference = F3075FA32E06960A00518A37 /* VoiceRecorder Watch AppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F3075FAC2E06960A00518A37 /* VoiceRecorder Watch AppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3075FC12E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch AppUITests" */;
			buildPhases = (
				F3075FA92E06960A00518A37 /* Sources */,
				F3075FAA2E06960A00518A37 /* Frameworks */,
				F3075FAB2E06960A00518A37 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F3075FAF2E06960A00518A37 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F3075FB02E06960A00518A37 /* VoiceRecorder Watch AppUITests */,
			);
			name = "VoiceRecorder Watch AppUITests";
			packageProductDependencies = (
			);
			productName = "VoiceRecorder Watch AppUITests";
			productReference = F3075FAD2E06960A00518A37 /* VoiceRecorder Watch AppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F3075F882E06960800518A37 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					F3075F8D2E06960800518A37 = {
						CreatedOnToolsVersion = 26.0;
					};
					F3075F932E06960800518A37 = {
						CreatedOnToolsVersion = 26.0;
					};
					F3075FA22E06960A00518A37 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = F3075F932E06960800518A37;
					};
					F3075FAC2E06960A00518A37 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = F3075F932E06960800518A37;
					};
				};
			};
			buildConfigurationList = F3075F8B2E06960800518A37 /* Build configuration list for PBXProject "VoiceRecorder" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F3075F872E06960800518A37;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = F3075F8F2E06960800518A37 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F3075F8D2E06960800518A37 /* VoiceRecorder */,
				F3075F932E06960800518A37 /* VoiceRecorder Watch App */,
				F3075FA22E06960A00518A37 /* VoiceRecorder Watch AppTests */,
				F3075FAC2E06960A00518A37 /* VoiceRecorder Watch AppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F3075F8C2E06960800518A37 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075F922E06960800518A37 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075FA12E06960A00518A37 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075FAB2E06960A00518A37 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F3075F902E06960800518A37 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075F9F2E06960A00518A37 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3075FA92E06960A00518A37 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F3075F972E06960800518A37 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3075F932E06960800518A37 /* VoiceRecorder Watch App */;
			targetProxy = F3075F962E06960800518A37 /* PBXContainerItemProxy */;
		};
		F3075FA52E06960A00518A37 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3075F932E06960800518A37 /* VoiceRecorder Watch App */;
			targetProxy = F3075FA42E06960A00518A37 /* PBXContainerItemProxy */;
		};
		F3075FAF2E06960A00518A37 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3075F932E06960800518A37 /* VoiceRecorder Watch App */;
			targetProxy = F3075FAE2E06960A00518A37 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F3075FB52E06960A00518A37 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F3075FB62E06960A00518A37 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		F3075FB82E06960A00518A37 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = VoiceRecorder;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cafelabs.VoiceRecorder.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		F3075FB92E06960A00518A37 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = VoiceRecorder;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cafelabs.VoiceRecorder.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		F3075FBC2E06960A00518A37 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_KEY_CFBundleDisplayName = VoiceRecorder;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cafelabs.VoiceRecorder;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		F3075FBD2E06960A00518A37 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_KEY_CFBundleDisplayName = VoiceRecorder;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cafelabs.VoiceRecorder;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F3075FBF2E06960A00518A37 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "cafelabs.VoiceRecorder-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VoiceRecorder Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VoiceRecorder Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		F3075FC02E06960A00518A37 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "cafelabs.VoiceRecorder-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VoiceRecorder Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VoiceRecorder Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		F3075FC22E06960A00518A37 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "cafelabs.VoiceRecorder-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "VoiceRecorder Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		F3075FC32E06960A00518A37 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "cafelabs.VoiceRecorder-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "VoiceRecorder Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F3075F8B2E06960800518A37 /* Build configuration list for PBXProject "VoiceRecorder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3075FB52E06960A00518A37 /* Debug */,
				F3075FB62E06960A00518A37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3075FB72E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3075FB82E06960A00518A37 /* Debug */,
				F3075FB92E06960A00518A37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3075FBB2E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3075FBC2E06960A00518A37 /* Debug */,
				F3075FBD2E06960A00518A37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3075FBE2E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch AppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3075FBF2E06960A00518A37 /* Debug */,
				F3075FC02E06960A00518A37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3075FC12E06960A00518A37 /* Build configuration list for PBXNativeTarget "VoiceRecorder Watch AppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3075FC22E06960A00518A37 /* Debug */,
				F3075FC32E06960A00518A37 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F3075F882E06960800518A37 /* Project object */;
}
