dependencies: \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/InterfaceController.m \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/InterfaceController.h \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS.sdk/System/Library/Frameworks/WatchKit.framework/Modules/module.modulemap \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS.sdk/System/Library/Frameworks/UserNotifications.framework/Modules/module.modulemap \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/RecordingManager.h \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS.sdk/System/Library/Frameworks/AVFoundation.framework/Modules/module.modulemap \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSNotificationService.h \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSMessage.h \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSDataManager.h
