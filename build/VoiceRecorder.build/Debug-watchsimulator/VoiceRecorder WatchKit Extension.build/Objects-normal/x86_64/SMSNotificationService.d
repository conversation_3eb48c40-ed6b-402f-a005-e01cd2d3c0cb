dependencies: \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSNotificationService.m \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSNotificationService.h \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchSimulator.platform/Developer/SDKs/WatchSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchSimulator.platform/Developer/SDKs/WatchSimulator.sdk/System/Library/Frameworks/UserNotifications.framework/Modules/module.modulemap \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSMessage.h \
  /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder\ WatchKit\ Extension/SMSDataManager.h \
  /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchSimulator.platform/Developer/SDKs/WatchSimulator.sdk/System/Library/Frameworks/WatchKit.framework/Modules/module.modulemap
