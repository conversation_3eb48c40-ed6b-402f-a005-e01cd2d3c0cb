/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o
/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o
/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o
/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o
/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o
/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o
