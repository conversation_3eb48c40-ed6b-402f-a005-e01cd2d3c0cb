{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build": {"is-mutated": true}, "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos": {"is-mutated": true}, "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos": {"is-mutated": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "/var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-scanning>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--end>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--linker-inputs-ready>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--modules-ready>", "<workspace-Debug--stale-file-removal>"], "outputs": ["<all>"]}, "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Assets.car", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-glance.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-notification.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap"], "roots": ["/tmp/VoiceRecorder.dst", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>"]}, "<workspace-Debug--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder-5064296dd7343c3bbcf28f9ceb0e5f2e-VFS-watchos/all-product-headers.yaml"], "outputs": ["<workspace-Debug--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache", "inputs": [], "outputs": ["/var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk", "-o", "/var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder.xcodeproj", "signature": "938533fbd24fd7b0eb3508b56701a50b"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder-5064296dd7343c3bbcf28f9ceb0e5f2e-VFS-watchos/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppExtensionInfoPlistGeneratorTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppExtensionInfoPlistGeneratorTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangePermissions>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-StripSymbols>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangePermissions>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangeAlternatePermissions>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CodeSign>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-GenerateStubAPI>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CopyAside>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductPostprocessingTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CodeSign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-Validate>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<Touch /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterProduct>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CopyAside>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-StripSymbols>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-Validate": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterExecutionPolicyException>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-Validate>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CustomTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--DocumentationTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ExtensionPointExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ExtensionPointExtractorTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GeneratedFilesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductStructureTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--HeadermapTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--InfoPlistTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleMapTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestEntryPointTaskProducerFactory>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleMapTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftPackageCopyFilesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--InfoPlistTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SanitizerTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftStandardLibrariesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftFrameworkABICheckerTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftABIBaselineGenerationTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestHostTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CopySwiftPackageResourcesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TAPISymbolExtractorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--DocumentationTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CustomTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--StubBinaryTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppExtensionInfoPlistGeneratorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ExtensionPointExtractorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppIntentsMetadataTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--start>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductStructureTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--HeadermapTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--RealityAssetsTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SanitizerTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--StubBinaryTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestEntryPointTaskProducerFactory": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--RealityAssetsTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestEntryPointTaskProducerFactory>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestHostTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductPostprocessingTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetTaskProducer>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--copy-headers-completion": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--copy-headers-completion>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Assets.car", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-glance.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-notification.plist", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o", "<Linked Binary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>"]}, "P0:::Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "--compile", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned", "--target-device", "watch", "--compress-pngs", "--enable-on-demand-resources", "NO", "--development-region", "en", "--minimum-deployment-target", "10.0", "--platform", "watchos"], "env": {}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "control-enabled": false, "deps": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "b9ab32ebfea08aad52bb2ac4d0db5fd9"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "--compile", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned", "--target-device", "watch", "--compress-pngs", "--enable-on-demand-resources", "NO", "--development-region", "en", "--minimum-deployment-target", "10.0", "--platform", "watchos"], "env": {}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "control-enabled": false, "deps": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "15b5844a97399f5948ed8cb1ffec3652"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileStoryboard /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit App/Base.lproj/Interface.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit App/Base.lproj/Interface.storyboard", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit App/Base.lproj/Interface.storyboard", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--target-device", "watch", "--module", "VoiceRecorder_WatchKit_Extension", "--output-partial-info-plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--minimum-deployment-target", "10.0", "--output-format", "human-readable-text", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit App/Base.lproj/Interface.storyboard", "--compilation-directory", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode-beta.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "control-enabled": false, "signature": "ca38e8f63feff8a5bb4f22c4a9bcd856"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VoiceRecorder.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-linking": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VoiceRecorder.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-linking>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-scanning": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VoiceRecorder.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-scanning>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--end": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Assets.car", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-glance.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-notification.plist", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan", "<Touch /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o", "<Linked Binary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppExtensionInfoPlistGeneratorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--AppIntentsMetadataTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangeAlternatePermissions>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-ChangePermissions>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CodeSign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CopyAside>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-GenerateStubAPI>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterExecutionPolicyException>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-RegisterProduct>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-StripSymbols>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-Validate>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CopySwiftPackageResourcesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--CustomTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--DocumentationTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ExtensionPointExtractorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--GeneratedFilesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--HeadermapTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--InfoPlistTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleMapTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductPostprocessingTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ProductStructureTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--RealityAssetsTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SanitizerTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--StubBinaryTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftABIBaselineGenerationTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftFrameworkABICheckerTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftPackageCopyFilesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--SwiftStandardLibrariesTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TAPISymbolExtractorTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestEntryPointTaskProducerFactory>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestHostTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetPostprocessingTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--TestTargetTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--copy-headers-completion>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--end>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VoiceRecorder.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-Debug-watchos--arm64-arm64_32-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VoiceRecorder.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos>", "<CreateBuildDirectory-/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<Linked Binary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--linker-inputs-ready>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--modules-ready": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--modules-ready>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Assets.car", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o", "<Linked Binary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-GenerateStubAPI>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--unsigned-product-ready>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Gate target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign": {"tool": "phony", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--unsigned-product-ready>"], "outputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:GenerateAssetSymbols /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets/", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "--compile", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "--target-device", "watch", "--compress-pngs", "--enable-on-demand-resources", "NO", "--development-region", "en", "--minimum-deployment-target", "10.0", "--platform", "watchos", "--bundle-identifier", "com.example.VoiceRecorder.watchkitapp.watchkitextension", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "control-enabled": false, "signature": "54b795680ca96105ed17fe7e4c6907d3"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:LinkAssetCatalog /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Assets.car"], "deps": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_dependencies"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-glance.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Base.lproj/Interface-notification.plist"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--target-device", "watch", "--module", "VoiceRecorder_WatchKit_Extension", "--minimum-deployment-target", "10.0", "--output-format", "human-readable-text", "--link", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode-beta.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "control-enabled": false, "signature": "b55371cdf2ebf3a73935e3a469c5be23"}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--start>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/thinned>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_output/unthinned>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Info.plist", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/Info.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Base.lproj/Interface-SBPartialInfo.plist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/assetcatalog_generated_info.plist", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/Info.plist"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-CodeSign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan"]}, "P0:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Touch /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--Barrier-Validate>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--will-sign>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--entry>"], "outputs": ["<Touch /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex"], "env": {}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "signature": "bad00922a11343b2665e45abdc154a25"}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/ExtensionDelegate.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/InterfaceController.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/RecordingManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSDataManager.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSMessage.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o"]}, "P1:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m normal arm64_32 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorder WatchKit Extension/SMSNotificationService.m", "<ClangStatCache /var/folders/40/d7z9jjwn4ml15twhkx4zm2680000gn/C/com.apple.DeveloperTools/26.0-17A5241e/Xcode/SDKStatCaches.noindex/watchos26.0-23R5280j-810b85d837118b2ee18c303afacf3d73.sdkstatcache>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o.scan", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder-5064296dd7343c3bbcf28f9ceb0e5f2e-VFS-watchos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder-5064296dd7343c3bbcf28f9ceb0e5f2e-VFS-watchos/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder-5064296dd7343c3bbcf28f9ceb0e5f2e-VFS-watchos/all-product-headers.yaml"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:CreateUniversalBinary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension normal arm64 arm64_32": {"tool": "shell", "description": "CreateUniversalBinary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension normal arm64 arm64_32", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-linking>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension", "<Linked Binary /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension>"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo", "-create", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "-output", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/VoiceRecorder WatchKit Extension.appex/VoiceRecorder WatchKit Extension"], "env": {}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "signature": "ddc7ef9ff51312da9d067ce7a3a98098"}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Ld /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension normal arm64": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension normal arm64", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-linking>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-watchos10.0", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk", "-O0", "-L/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "-L/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "-F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "-F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "-filelist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat", "-e", "_WKExtensionMain", "-fobjc-arc", "-fobjc-link-runtime", "-fapplication-extension", "-framework", "WatchKit", "-o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/Binary/VoiceRecorder WatchKit Extension"], "env": {"PATH": "/Applications/Xcode-beta.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/usr/local/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Library/Frameworks/Mono.framework/Versions/Current/Commands"}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "deps": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension_dependency_info.dat"], "deps-style": "dependency-info", "signature": "235517c9d2af66ef90c9d48835f47dd3"}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:Ld /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension normal arm64_32": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension normal arm64_32", "inputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/InterfaceController.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/ExtensionDelegate.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/RecordingManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSMessage.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSDataManager.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/SMSNotificationService.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--swift-generated-headers>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--ModuleVerifierTaskProducer>", "<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--begin-linking>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat"], "args": ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64_32-apple-watchos10.0", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk", "-O0", "-L/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "-L/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "-F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/EagerLinkingTBDs/Debug-watchos", "-F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos", "-filelist", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat", "-e", "_WKExtensionMain", "-fobjc-arc", "-fobjc-link-runtime", "-fapplication-extension", "-framework", "WatchKit", "-o", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/Binary/VoiceRecorder WatchKit Extension"], "env": {"PATH": "/Applications/Xcode-beta.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/usr/local/bin:/Applications/Xcode-beta.app/Contents/Developer/usr/bin:/Applications/Xcode-beta.app/Contents/Developer/usr/local/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Library/Frameworks/Mono.framework/Versions/Current/Commands"}, "working-directory": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch", "deps": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension_dependency_info.dat"], "deps-style": "dependency-info", "signature": "dc6df64be794b16f61353be09b7430d6"}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/VoiceRecorder WatchKit Extension.LinkFileList"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/VoiceRecorder WatchKit Extension.LinkFileList"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/Objects-normal/arm64_32/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-non-framework-target-headers.hmap"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyMetadataFileList"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.DependencyStaticMetadataFileList"]}, "P2:target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap", "inputs": ["<target-VoiceRecorder WatchKit Extension-5064296dd7343c3bbcf28f9ceb0e5f2e76105802a4a619f7953a522bd4fc77fe--immediate>"], "outputs": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension.hmap"]}}}