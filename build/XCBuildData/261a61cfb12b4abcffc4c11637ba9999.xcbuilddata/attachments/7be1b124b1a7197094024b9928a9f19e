-target arm64-apple-watchos10.0 '-std=gnu17' -fobjc-arc -fobjc-weak -fmodules -gmodules -fapplication-extension -fpascal-strings -O0 -fno-common '-DDEBUG=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchOS.platform/Developer/SDKs/WatchOS26.0.sdk -g -iquote '/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap' -iquote '/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap' -I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos/include '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources-normal/arm64' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources/arm64' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchos/VoiceRecorder WatchKit Extension.build/DerivedSources' -F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchos