-target arm64-apple-watchos10.0-simulator '-std=gnu17' -fobjc-arc -fobjc-weak -fmodules -gmodules -fapplication-extension -fpascal-strings -O0 -fno-common '-DDEBUG=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode-beta.app/Contents/Developer/Platforms/WatchSimulator.platform/Developer/SDKs/WatchSimulator26.0.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote '/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-generated-files.hmap' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-own-target-headers.hmap' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-all-target-headers.hmap' -iquote '/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/VoiceRecorder WatchKit Extension-project-headers.hmap' -I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchsimulator/include '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/DerivedSources-normal/arm64' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/DerivedSources/arm64' '-I/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/VoiceRecorder.build/Debug-watchsimulator/VoiceRecorder WatchKit Extension.build/DerivedSources' -F/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/build/Debug-watchsimulator