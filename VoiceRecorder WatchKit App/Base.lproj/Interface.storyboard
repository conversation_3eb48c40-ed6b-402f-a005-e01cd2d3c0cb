<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder.WatchKit.Storyboard" version="3.0" toolsVersion="22155" targetRuntime="watchKit" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="AgC-eL-Hgc">
    <device id="watch44" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22131"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBWatchKitPlugin" version="22054"/>
    </dependencies>
    <scenes>
        <!--Interface Controller-->
        <scene sceneID="aou-V4-d1y">
            <objects>
                <controller identifier="InterfaceController" id="AgC-eL-Hgc" customClass="InterfaceController">
                    <items>
                        <label alignment="center" text="00:00.00" id="Xcc-4E-5uL">
                            <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </label>
                        <group width="1" height="40" alignment="center" id="9pK-6E-BwX">
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                        </group>
                        <group width="1" alignment="center" layout="horizontal" id="Nha-qg-ufX">
                            <items>
                                <button width="60" height="60" alignment="center" title="●" id="Iqb-6E-g3d">
                                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                                    <connections>
                                        <action selector="recordButtonTapped" destination="AgC-eL-Hgc" id="5Nq-Nh-fhc"/>
                                    </connections>
                                </button>
                                <button width="60" height="60" alignment="center" title="⏸" id="Jvg-8E-6Yc">
                                    <color key="backgroundColor" red="1" green="0.64705882349999999" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                                    <connections>
                                        <action selector="pauseButtonTapped" destination="AgC-eL-Hgc" id="Xhg-aE-8Yc"/>
                                    </connections>
                                </button>
                                <button width="60" height="60" alignment="center" title="⏹" id="Yxc-4E-5uL">
                                    <color key="backgroundColor" red="0.50196078430000002" green="0.50196078430000002" blue="0.50196078430000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                                    <connections>
                                        <action selector="stopButtonTapped" destination="AgC-eL-Hgc" id="Zhg-bE-9Yc"/>
                                    </connections>
                                </button>
                            </items>
                        </group>
                        <button width="1" alignment="center" title="Recordings" id="Abc-1E-2uL">
                            <color key="backgroundColor" red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <connections>
                                <action selector="listButtonTapped" destination="AgC-eL-Hgc" id="Def-2E-3uL"/>
                            </connections>
                        </button>
                    </items>
                    <connections>
                        <outlet property="listButton" destination="Abc-1E-2uL" id="Ghi-3E-4uL"/>
                        <outlet property="pauseButton" destination="Jvg-8E-6Yc" id="Jkl-4E-5uL"/>
                        <outlet property="recordButton" destination="Iqb-6E-g3d" id="Mno-5E-6uL"/>
                        <outlet property="stopButton" destination="Yxc-4E-5uL" id="Pqr-6E-7uL"/>
                        <outlet property="timeLabel" destination="Xcc-4E-5uL" id="Stu-7E-8uL"/>
                        <outlet property="waveformGroup" destination="9pK-6E-BwX" id="Vwx-8E-9uL"/>
                    </connections>
                </controller>
            </objects>
            <point key="canvasLocation" x="220" y="345"/>
        </scene>
        <!--Recording List Interface Controller-->
        <scene sceneID="bcd-V5-e2z">
            <objects>
                <controller identifier="RecordingListInterfaceController" id="Efg-fM-Ihc" customClass="RecordingListInterfaceController">
                    <items>
                        <table alignment="left" id="Hij-gN-Jkl">
                            <items>
                                <tableRow identifier="RecordingRowController" id="Lmn-hO-Opq" customClass="RecordingRowController">
                                    <group key="rootItem" width="1" alignment="left" id="Rst-iP-Uvw">
                                        <items>
                                            <label alignment="left" text="Recording Title" id="Xyz-jQ-Abc">
                                                <fontDescription key="font" style="UICTFontTextStyleHeadline"/>
                                            </label>
                                            <label alignment="left" text="Details" id="Def-kR-Ghi">
                                                <fontDescription key="font" style="UICTFontTextStyleCaption1"/>
                                                <color key="textColor" red="0.60000002379999995" green="0.60000002379999995" blue="0.60000002379999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </label>
                                        </items>
                                    </group>
                                    <connections>
                                        <outlet property="detailLabel" destination="Def-kR-Ghi" id="Jkl-lS-Mno"/>
                                        <outlet property="titleLabel" destination="Xyz-jQ-Abc" id="Pqr-mT-Stu"/>
                                    </connections>
                                </tableRow>
                            </items>
                        </table>
                    </items>
                    <connections>
                        <outlet property="recordingsTable" destination="Hij-gN-Jkl" id="Vwx-nU-Yzb"/>
                    </connections>
                </controller>
            </objects>
            <point key="canvasLocation" x="468" y="345"/>
        </scene>
        <!--Recording Detail Interface Controller-->
        <scene sceneID="cde-V6-f3a">
            <objects>
                <controller identifier="RecordingDetailInterfaceController" id="Ghi-oV-Jkl" customClass="RecordingDetailInterfaceController">
                    <items>
                        <label alignment="center" text="Recording Title" id="Mno-pW-Qrs">
                            <fontDescription key="font" style="UICTFontTextStyleHeadline"/>
                        </label>
                        <label alignment="center" text="00:00.00" id="Tuv-qX-Wxy">
                            <fontDescription key="font" style="UICTFontTextStyleSubheadline"/>
                            <color key="textColor" red="0.60000002379999995" green="0.60000002379999995" blue="0.60000002379999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </label>
                        <slider width="1" alignment="center" value="0.5" id="Zab-rY-Cde"/>
                        <group width="1" alignment="center" layout="horizontal" id="Fgh-sZ-Ijk">
                            <items>
                                <button width="60" height="60" alignment="center" title="▶" id="Lmn-tA-Opq">
                                    <color key="backgroundColor" red="0.0" green="0.78431372549999998" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                                    <connections>
                                        <action selector="playButtonTapped" destination="Ghi-oV-Jkl" id="Rst-uB-Vwx"/>
                                    </connections>
                                </button>
                                <button width="60" height="60" alignment="center" title="🗑" id="Yza-vC-Bcd">
                                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <fontDescription key="font" style="UICTFontTextStyleTitle1"/>
                                    <connections>
                                        <action selector="deleteButtonTapped" destination="Ghi-oV-Jkl" id="Efg-wD-Hij"/>
                                    </connections>
                                </button>
                            </items>
                        </group>
                    </items>
                    <connections>
                        <outlet property="deleteButton" destination="Yza-vC-Bcd" id="Klm-xE-Nop"/>
                        <outlet property="durationLabel" destination="Tuv-qX-Wxy" id="Qrs-yF-Tuv"/>
                        <outlet property="playButton" destination="Lmn-tA-Opq" id="Wxy-zG-Zab"/>
                        <outlet property="progressSlider" destination="Zab-rY-Cde" id="Cde-AH-Fgh"/>
                        <outlet property="titleLabel" destination="Mno-pW-Qrs" id="Ijk-BI-Lmn"/>
                    </connections>
                </controller>
            </objects>
            <point key="canvasLocation" x="716" y="345"/>
        </scene>
    </scenes>
</document>
