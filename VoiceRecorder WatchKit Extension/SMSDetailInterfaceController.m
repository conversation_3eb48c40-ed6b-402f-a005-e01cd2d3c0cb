//
//  SMSDetailInterfaceController.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "SMSDetailInterfaceController.h"
#import "SMSMessage.h"
#import "SMSDataManager.h"

@interface SMSDetailInterfaceController ()
@property (nonatomic, strong) SMSMessage *message;
@end

@implementation SMSDetailInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
    if ([context isKindOfClass:[SMSMessage class]]) {
        self.message = (SMSMessage *)context;
        [self setupInterface];
    }
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (void)setupInterface {
    if (!self.message) return;
    
    // Set sender information
    NSString *senderText = self.message.senderName ?: self.message.sender;
    [self.senderLabel setText:senderText];
    
    // Set timestamp
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateStyle = NSDateFormatterMediumStyle;
    formatter.timeStyle = NSDateFormatterShortStyle;
    [self.timeLabel setText:[formatter stringFromDate:self.message.timestamp]];
    
    // Set content
    [self.contentLabel setText:self.message.content];
    
    // Mark as read
    [[SMSDataManager sharedManager] markMessageAsRead:self.message.messageID];
}

- (IBAction)deleteButtonTapped {
    WKAlertAction *deleteAction = [WKAlertAction actionWithTitle:@"Delete" 
                                                           style:WKAlertActionStyleDestructive 
                                                         handler:^{
        [[SMSDataManager sharedManager] deleteMessage:self.message.messageID];
        [self popController];
    }];
    
    WKAlertAction *cancelAction = [WKAlertAction actionWithTitle:@"Cancel" 
                                                           style:WKAlertActionStyleCancel 
                                                         handler:nil];
    
    [self presentAlertControllerWithTitle:@"Delete Message" 
                                  message:@"Are you sure you want to delete this message?" 
                           preferredStyle:WKAlertControllerStyleAlert 
                                  actions:@[deleteAction, cancelAction]];
}

@end
