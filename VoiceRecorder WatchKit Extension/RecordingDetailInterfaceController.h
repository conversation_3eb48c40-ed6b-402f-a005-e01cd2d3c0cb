//
//  RecordingDetailInterfaceController.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <WatchKit/WatchKit.h>
#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

@interface RecordingDetailInterfaceController : WKInterfaceController

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *titleLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *durationLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *playButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *deleteButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceSlider *progressSlider;

- (IBAction)playButtonTapped;
- (IBAction)deleteButtonTapped;

@end
