//
//  SMSMessage.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "SMSMessage.h"

@implementation SMSMessage

+ (BOOL)supportsSecureCoding {
    return YES;
}

- (instancetype)initWithContent:(NSString *)content 
                         sender:(NSString *)sender 
                     senderName:(NSString *)senderName 
                      timestamp:(NSDate *)timestamp {
    self = [super init];
    if (self) {
        _messageID = [[NSUUID UUID] UUIDString];
        _content = [content copy];
        _sender = [sender copy];
        _senderName = [senderName copy];
        _timestamp = timestamp ?: [NSDate date];
        _isRead = NO;
        _threadID = [sender copy]; // Use sender as thread ID for simplicity
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super init];
    if (self) {
        _messageID = [coder decodeObjectOfClass:[NSString class] forKey:@"messageID"];
        _content = [coder decodeObjectOfClass:[NSString class] forKey:@"content"];
        _sender = [coder decodeObjectOfClass:[NSString class] forKey:@"sender"];
        _senderName = [coder decodeObjectOfClass:[NSString class] forKey:@"senderName"];
        _timestamp = [coder decodeObjectOfClass:[NSDate class] forKey:@"timestamp"];
        _isRead = [coder decodeBoolForKey:@"isRead"];
        _threadID = [coder decodeObjectOfClass:[NSString class] forKey:@"threadID"];
    }
    return self;
}

- (void)encodeWithCoder:(NSCoder *)coder {
    [coder encodeObject:_messageID forKey:@"messageID"];
    [coder encodeObject:_content forKey:@"content"];
    [coder encodeObject:_sender forKey:@"sender"];
    [coder encodeObject:_senderName forKey:@"senderName"];
    [coder encodeObject:_timestamp forKey:@"timestamp"];
    [coder encodeBool:_isRead forKey:@"isRead"];
    [coder encodeObject:_threadID forKey:@"threadID"];
}

- (NSDictionary *)toDictionary {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    if (self.messageID) dict[@"messageID"] = self.messageID;
    if (self.content) dict[@"content"] = self.content;
    if (self.sender) dict[@"sender"] = self.sender;
    if (self.senderName) dict[@"senderName"] = self.senderName;
    if (self.timestamp) dict[@"timestamp"] = @([self.timestamp timeIntervalSince1970]);
    dict[@"isRead"] = @(self.isRead);
    if (self.threadID) dict[@"threadID"] = self.threadID;
    
    return [dict copy];
}

+ (instancetype)fromDictionary:(NSDictionary *)dictionary {
    SMSMessage *message = [[SMSMessage alloc] init];
    
    message.messageID = dictionary[@"messageID"];
    message.content = dictionary[@"content"];
    message.sender = dictionary[@"sender"];
    message.senderName = dictionary[@"senderName"];
    
    NSNumber *timestampNumber = dictionary[@"timestamp"];
    if (timestampNumber) {
        message.timestamp = [NSDate dateWithTimeIntervalSince1970:[timestampNumber doubleValue]];
    }
    
    message.isRead = [dictionary[@"isRead"] boolValue];
    message.threadID = dictionary[@"threadID"];
    
    return message;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"SMSMessage: %@ from %@ (%@) at %@", 
            self.content, self.senderName ?: self.sender, self.sender, self.timestamp];
}

@end
