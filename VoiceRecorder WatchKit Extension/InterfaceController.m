//
//  InterfaceController.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "InterfaceController.h"
#import "SMSDataManager.h"

@interface InterfaceController ()
@property (nonatomic, strong) RecordingManager *recordingManager;
@property (nonatomic, strong) SMSNotificationService *smsService;
@end

@implementation InterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];

    // Configure interface objects here.
    self.recordingManager = [RecordingManager sharedManager];
    self.recordingManager.delegate = self;

    // Setup SMS service
    self.smsService = [SMSNotificationService sharedService];
    self.smsService.delegate = self;
    [self.smsService requestNotificationPermissions];

    [self updateUI];
    [self updateSMSCount];
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
    [self updateUI];
    [self updateSMSCount];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (void)updateUI {
    RecordingState state = self.recordingManager.state;
    
    switch (state) {
        case RecordingStateStopped:
            [self.recordButton setEnabled:YES];
            [self.recordButton setTitle:@"●"];
            [self.recordButton setBackgroundColor:[UIColor redColor]];
            [self.pauseButton setEnabled:NO];
            [self.pauseButton setHidden:YES];
            [self.stopButton setEnabled:NO];
            [self.stopButton setHidden:YES];
            [self.listButton setEnabled:YES];
            [self.listButton setHidden:NO];
            [self.timeLabel setText:@"00:00.00"];
            break;
            
        case RecordingStateRecording:
            [self.recordButton setEnabled:NO];
            [self.recordButton setHidden:YES];
            [self.pauseButton setEnabled:YES];
            [self.pauseButton setHidden:NO];
            [self.pauseButton setTitle:@"⏸"];
            [self.pauseButton setBackgroundColor:[UIColor orangeColor]];
            [self.stopButton setEnabled:YES];
            [self.stopButton setHidden:NO];
            [self.stopButton setTitle:@"⏹"];
            [self.stopButton setBackgroundColor:[UIColor grayColor]];
            [self.listButton setHidden:YES];
            break;
            
        case RecordingStatePaused:
            [self.recordButton setEnabled:NO];
            [self.recordButton setHidden:YES];
            [self.pauseButton setEnabled:YES];
            [self.pauseButton setHidden:NO];
            [self.pauseButton setTitle:@"▶"];
            [self.pauseButton setBackgroundColor:[UIColor greenColor]];
            [self.stopButton setEnabled:YES];
            [self.stopButton setHidden:NO];
            [self.stopButton setTitle:@"⏹"];
            [self.stopButton setBackgroundColor:[UIColor grayColor]];
            [self.listButton setHidden:YES];
            break;
    }
    
    // Update waveform visualization
    [self updateWaveform];
}

- (void)updateWaveform {
    // Simple waveform visualization using background color animation
    if (self.recordingManager.state == RecordingStateRecording) {
        [self.waveformGroup setBackgroundColor:[UIColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:0.3]];
        
        // Animate the waveform
        [self animateWithDuration:0.5 animations:^{
            [self.waveformGroup setBackgroundColor:[UIColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:0.1]];
        }];
    } else {
        [self.waveformGroup setBackgroundColor:[UIColor clearColor]];
    }
}

- (IBAction)recordButtonTapped {
    BOOL success = [self.recordingManager startRecording];
    if (!success) {
        [self presentAlertControllerWithTitle:@"Error" 
                                      message:@"Failed to start recording. Please check microphone permissions." 
                               preferredStyle:WKAlertControllerStyleAlert 
                                      actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
    }
}

- (IBAction)pauseButtonTapped {
    if (self.recordingManager.state == RecordingStateRecording) {
        [self.recordingManager pauseRecording];
    } else if (self.recordingManager.state == RecordingStatePaused) {
        [self.recordingManager resumeRecording];
    }
}

- (IBAction)stopButtonTapped {
    [self.recordingManager stopRecording];
}

- (IBAction)listButtonTapped {
    [self pushControllerWithName:@"RecordingListInterfaceController" context:nil];
}

- (IBAction)smsButtonTapped {
    [self pushControllerWithName:@"SMSListInterfaceController" context:nil];
}

- (void)updateSMSCount {
    NSInteger unreadCount = [[SMSDataManager sharedManager] getUnreadMessageCount];
    if (unreadCount > 0) {
        [self.smsCountLabel setText:[NSString stringWithFormat:@"%ld", (long)unreadCount]];
        [self.smsCountLabel setHidden:NO];
        [self.smsCountLabel setBackgroundColor:[UIColor redColor]];
    } else {
        [self.smsCountLabel setHidden:YES];
    }
}

#pragma mark - RecordingManagerDelegate

- (void)recordingStateDidChange:(RecordingState)state {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateUI];
    });
}

- (void)recordingTimeDidUpdate:(NSTimeInterval)time {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *timeString = [self.recordingManager formatTime:time];
        [self.timeLabel setText:timeString];
        [self updateWaveform];
    });
}

- (void)recordingDidFinishWithURL:(NSURL *)url {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self presentAlertControllerWithTitle:@"Recording Saved" 
                                      message:@"Your recording has been saved successfully." 
                               preferredStyle:WKAlertControllerStyleAlert 
                                      actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
    });
}

- (void)recordingDidFailWithError:(NSError *)error {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self presentAlertControllerWithTitle:@"Recording Error"
                                      message:error.localizedDescription
                               preferredStyle:WKAlertControllerStyleAlert
                                      actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
    });
}

#pragma mark - SMSNotificationServiceDelegate

- (void)didReceiveNewSMSMessage:(SMSMessage *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateSMSCount];

        // Show brief notification on main screen
        [WKInterfaceDevice currentDevice].playHaptic(WKHapticTypeNotification);
    });
}

- (void)smsNotificationServiceDidFailWithError:(NSError *)error {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSLog(@"SMS Service Error: %@", error.localizedDescription);
    });
}

@end
