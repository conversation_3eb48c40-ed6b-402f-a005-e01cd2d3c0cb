//
//  RecordingManager.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

typedef NS_ENUM(NSInteger, RecordingState) {
    RecordingStateStopped,
    RecordingStateRecording,
    RecordingStatePaused
};

@protocol RecordingManagerDelegate <NSObject>
- (void)recordingStateDidChange:(RecordingState)state;
- (void)recordingTimeDidUpdate:(NSTimeInterval)time;
- (void)recordingDidFinishWithURL:(NSURL *)url;
- (void)recordingDidFailWithError:(NSError *)error;
@end

@interface RecordingManager : NSObject <AVAudioRecorderDelegate>

@property (nonatomic, weak) id<RecordingManagerDelegate> delegate;
@property (nonatomic, readonly) RecordingState state;
@property (nonatomic, readonly) NSTimeInterval currentTime;
@property (nonatomic, readonly) NSURL *currentRecordingURL;

+ (instancetype)sharedManager;

- (BOOL)startRecording;
- (void)pauseRecording;
- (void)resumeRecording;
- (void)stopRecording;
- (void)cancelRecording;

- (NSArray<NSURL *> *)getAllRecordings;
- (BOOL)deleteRecordingAtURL:(NSURL *)url;
- (NSString *)formatTime:(NSTimeInterval)time;

@end
