//
//  SMSMessage.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <Foundation/Foundation.h>

@interface SMSMessage : NSObject <NSCoding, NSSecureCoding>

@property (nonatomic, strong) NSString *messageID;
@property (nonatomic, strong) NSString *content;
@property (nonatomic, strong) NSString *sender;
@property (nonatomic, strong) NSString *senderName;
@property (nonatomic, strong) NSDate *timestamp;
@property (nonatomic, assign) BOOL isRead;
@property (nonatomic, strong) NSString *threadID;

- (instancetype)initWithContent:(NSString *)content 
                         sender:(NSString *)sender 
                     senderName:(NSString *)senderName 
                      timestamp:(NSDate *)timestamp;

- (NSDictionary *)toDictionary;
+ (instancetype)fromDictionary:(NSDictionary *)dictionary;

@end
