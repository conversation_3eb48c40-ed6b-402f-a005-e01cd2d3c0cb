//
//  RecordingListInterfaceController.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "RecordingListInterfaceController.h"
#import "RecordingManager.h"
#import "RecordingRowController.h"

@interface RecordingListInterfaceController ()
@property (nonatomic, strong) NSArray<NSURL *> *recordings;
@end

@implementation RecordingListInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
    [self loadRecordings];
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
    [self loadRecordings];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (void)loadRecordings {
    self.recordings = [[RecordingManager sharedManager] getAllRecordings];
    [self setupTable];
}

- (void)setupTable {
    [self.recordingsTable setNumberOfRows:self.recordings.count withRowType:@"RecordingRowController"];
    
    for (NSInteger i = 0; i < self.recordings.count; i++) {
        RecordingRowController *rowController = [self.recordingsTable rowControllerAtIndex:i];
        NSURL *recordingURL = self.recordings[i];
        
        // Get file name without extension
        NSString *fileName = [recordingURL.lastPathComponent stringByDeletingPathExtension];
        
        // Extract date from filename
        NSString *displayName = fileName;
        if ([fileName hasPrefix:@"Recording_"]) {
            NSString *dateString = [fileName substringFromIndex:10]; // Remove "Recording_" prefix
            displayName = [self formatDateString:dateString];
        }
        
        // Get file size
        NSError *error;
        NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:recordingURL.path error:&error];
        NSNumber *fileSize = attributes[NSFileSize];
        NSString *sizeString = [self formatFileSize:fileSize.longLongValue];
        
        [rowController.titleLabel setText:displayName];
        [rowController.detailLabel setText:sizeString];
    }
}

- (NSString *)formatDateString:(NSString *)dateString {
    // Convert "yyyy-MM-dd_HH-mm-ss" to readable format
    NSDateFormatter *inputFormatter = [[NSDateFormatter alloc] init];
    inputFormatter.dateFormat = @"yyyy-MM-dd_HH-mm-ss";
    
    NSDate *date = [inputFormatter dateFromString:dateString];
    if (!date) {
        return dateString;
    }
    
    NSDateFormatter *outputFormatter = [[NSDateFormatter alloc] init];
    outputFormatter.dateStyle = NSDateFormatterShortStyle;
    outputFormatter.timeStyle = NSDateFormatterShortStyle;
    
    return [outputFormatter stringFromDate:date];
}

- (NSString *)formatFileSize:(long long)bytes {
    if (bytes < 1024) {
        return [NSString stringWithFormat:@"%lld B", bytes];
    } else if (bytes < 1024 * 1024) {
        return [NSString stringWithFormat:@"%.1f KB", bytes / 1024.0];
    } else {
        return [NSString stringWithFormat:@"%.1f MB", bytes / (1024.0 * 1024.0)];
    }
}

- (void)table:(WKInterfaceTable *)table didSelectRowAtIndex:(NSInteger)rowIndex {
    NSURL *selectedRecording = self.recordings[rowIndex];
    [self pushControllerWithName:@"RecordingDetailInterfaceController" context:selectedRecording];
}

@end
