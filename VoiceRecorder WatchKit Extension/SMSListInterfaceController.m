//
//  SMSListInterfaceController.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "SMSListInterfaceController.h"
#import "SMSDataManager.h"
#import "SMSRowController.h"
#import "SMSMessage.h"

@interface SMSListInterfaceController ()
@property (nonatomic, strong) NSArray<SMSMessage *> *messages;
@property (nonatomic, strong) SMSNotificationService *smsService;
@end

@implementation SMSListInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
    self.smsService = [SMSNotificationService sharedService];
    self.smsService.delegate = self;
    
    [self loadMessages];
    [self updateStatusLabel];
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
    [self loadMessages];
    [self updateStatusLabel];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
}

- (void)loadMessages {
    self.messages = [[SMSDataManager sharedManager] getAllMessages];
    [self setupTable];
}

- (void)setupTable {
    [self.smsTable setNumberOfRows:self.messages.count withRowType:@"SMSRowController"];
    
    for (NSInteger i = 0; i < self.messages.count; i++) {
        SMSRowController *rowController = [self.smsTable rowControllerAtIndex:i];
        SMSMessage *message = self.messages[i];
        
        [rowController.senderLabel setText:message.senderName ?: message.sender];
        [rowController.contentLabel setText:message.content];
        [rowController.timeLabel setText:[self formatTime:message.timestamp]];
        
        // Set different colors for read/unread messages
        if (message.isRead) {
            [rowController.senderLabel setTextColor:[UIColor lightGrayColor]];
            [rowController.contentLabel setTextColor:[UIColor lightGrayColor]];
        } else {
            [rowController.senderLabel setTextColor:[UIColor whiteColor]];
            [rowController.contentLabel setTextColor:[UIColor whiteColor]];
        }
        
        // Add unread indicator
        if (!message.isRead) {
            [rowController.unreadIndicator setHidden:NO];
            [rowController.unreadIndicator setBackgroundColor:[UIColor blueColor]];
        } else {
            [rowController.unreadIndicator setHidden:YES];
        }
    }
}

- (void)updateStatusLabel {
    NSInteger totalCount = [[SMSDataManager sharedManager] getTotalMessageCount];
    NSInteger unreadCount = [[SMSDataManager sharedManager] getUnreadMessageCount];
    
    if (totalCount == 0) {
        [self.statusLabel setText:@"No messages"];
    } else {
        NSString *statusText = [NSString stringWithFormat:@"%ld messages (%ld unread)", 
                               (long)totalCount, (long)unreadCount];
        [self.statusLabel setText:statusText];
    }
}

- (NSString *)formatTime:(NSDate *)date {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    
    NSCalendar *calendar = [NSCalendar currentCalendar];
    if ([calendar isDateInToday:date]) {
        formatter.dateFormat = @"HH:mm";
    } else if ([calendar isDateInYesterday:date]) {
        return @"Yesterday";
    } else {
        formatter.dateFormat = @"MM/dd";
    }
    
    return [formatter stringFromDate:date];
}

- (void)table:(WKInterfaceTable *)table didSelectRowAtIndex:(NSInteger)rowIndex {
    if (rowIndex < self.messages.count) {
        SMSMessage *selectedMessage = self.messages[rowIndex];
        
        // Mark as read
        [[SMSDataManager sharedManager] markMessageAsRead:selectedMessage.messageID];
        
        // Push to detail view
        [self pushControllerWithName:@"SMSDetailInterfaceController" context:selectedMessage];
    }
}

- (IBAction)clearAllButtonTapped {
    WKAlertAction *deleteAction = [WKAlertAction actionWithTitle:@"Delete All" 
                                                           style:WKAlertActionStyleDestructive 
                                                         handler:^{
        [[SMSDataManager sharedManager] deleteAllMessages];
        [self loadMessages];
        [self updateStatusLabel];
    }];
    
    WKAlertAction *cancelAction = [WKAlertAction actionWithTitle:@"Cancel" 
                                                           style:WKAlertActionStyleCancel 
                                                         handler:nil];
    
    [self presentAlertControllerWithTitle:@"Delete All Messages" 
                                  message:@"Are you sure you want to delete all SMS messages?" 
                           preferredStyle:WKAlertControllerStyleAlert 
                                  actions:@[deleteAction, cancelAction]];
}

- (IBAction)addTestSMSButtonTapped {
    // Add a test SMS message
    NSArray *testMessages = @[
        @{@"content": @"This is a test message", @"sender": @"+1111111111", @"name": @"Test Contact"},
        @{@"content": @"Another test message with longer content to see how it displays", @"sender": @"+2222222222", @"name": @"Another Contact"},
        @{@"content": @"Short msg", @"sender": @"+3333333333", @"name": @"Short Name"}
    ];
    
    NSDictionary *testMsg = testMessages[arc4random() % testMessages.count];
    
    [self.smsService addManualSMS:testMsg[@"content"] 
                       fromSender:testMsg[@"sender"] 
                       senderName:testMsg[@"name"]];
}

#pragma mark - SMSNotificationServiceDelegate

- (void)didReceiveNewSMSMessage:(SMSMessage *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self loadMessages];
        [self updateStatusLabel];
        
        // Show brief notification
        [self presentAlertControllerWithTitle:@"New SMS" 
                                      message:[NSString stringWithFormat:@"From: %@", message.senderName ?: message.sender]
                               preferredStyle:WKAlertControllerStyleAlert 
                                      actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
    });
}

- (void)smsNotificationServiceDidFailWithError:(NSError *)error {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self presentAlertControllerWithTitle:@"SMS Service Error" 
                                      message:error.localizedDescription 
                               preferredStyle:WKAlertControllerStyleAlert 
                                      actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
    });
}

@end
