//
//  SMSDataManager.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <Foundation/Foundation.h>
#import "SMSMessage.h"

@interface SMSDataManager : NSObject

+ (instancetype)sharedManager;

// Message management
- (void)saveMessage:(SMSMessage *)message;
- (NSArray<SMSMessage *> *)getAllMessages;
- (NSArray<SMSMessage *> *)getMessagesFromSender:(NSString *)sender;
- (NSArray<SMSMessage *> *)getUnreadMessages;
- (SMSMessage *)getMessageByID:(NSString *)messageID;

// Message operations
- (void)markMessageAsRead:(NSString *)messageID;
- (void)deleteMessage:(NSString *)messageID;
- (void)deleteAllMessages;

// Statistics
- (NSInteger)getTotalMessageCount;
- (NSInteger)getUnreadMessageCount;
- (NSArray<NSString *> *)getAllSenders;

// Data persistence
- (BOOL)exportMessagesToFile:(NSURL *)fileURL;
- (BOOL)importMessagesFromFile:(NSURL *)fileURL;

@end
