//
//  RecordingManager.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "RecordingManager.h"
#import <WatchKit/WatchKit.h>

@interface RecordingManager ()
@property (nonatomic, strong) AVAudioRecorder *audioRecorder;
@property (nonatomic, strong) AVAudioSession *audioSession;
@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, assign) RecordingState state;
@property (nonatomic, assign) NSTimeInterval currentTime;
@property (nonatomic, strong) NSURL *currentRecordingURL;
@property (nonatomic, strong) NSDateFormatter *dateFormatter;
@end

@implementation RecordingManager

+ (instancetype)sharedManager {
    static RecordingManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[RecordingManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _state = RecordingStateStopped;
        _currentTime = 0.0;
        _audioSession = [AVAudioSession sharedInstance];
        
        _dateFormatter = [[NSDateFormatter alloc] init];
        _dateFormatter.dateFormat = @"yyyy-MM-dd_HH-mm-ss";
        
        [self setupAudioSession];
    }
    return self;
}

- (void)setupAudioSession {
    NSError *error = nil;
    [self.audioSession setCategory:AVAudioSessionCategoryRecord error:&error];
    if (error) {
        NSLog(@"Error setting audio session category: %@", error.localizedDescription);
    }
}

- (BOOL)startRecording {
    if (self.state != RecordingStateStopped) {
        return NO;
    }
    
    NSError *error = nil;
    [self.audioSession setActive:YES error:&error];
    if (error) {
        [self.delegate recordingDidFailWithError:error];
        return NO;
    }
    
    // Create recording URL
    NSURL *documentsURL = [[[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
    NSString *fileName = [NSString stringWithFormat:@"Recording_%@.m4a", [self.dateFormatter stringFromDate:[NSDate date]]];
    self.currentRecordingURL = [documentsURL URLByAppendingPathComponent:fileName];
    
    // Audio settings
    NSDictionary *settings = @{
        AVFormatIDKey: @(kAudioFormatMPEG4AAC),
        AVSampleRateKey: @44100.0,
        AVNumberOfChannelsKey: @1,
        AVEncoderAudioQualityKey: @(AVAudioQualityMedium)
    };
    
    self.audioRecorder = [[AVAudioRecorder alloc] initWithURL:self.currentRecordingURL settings:settings error:&error];
    if (error) {
        [self.delegate recordingDidFailWithError:error];
        return NO;
    }
    
    self.audioRecorder.delegate = self;
    self.audioRecorder.meteringEnabled = YES;
    
    if ([self.audioRecorder record]) {
        self.state = RecordingStateRecording;
        self.currentTime = 0.0;
        [self startTimer];
        [self.delegate recordingStateDidChange:self.state];
        return YES;
    } else {
        [self.delegate recordingDidFailWithError:[NSError errorWithDomain:@"RecordingError" code:1 userInfo:@{NSLocalizedDescriptionKey: @"Failed to start recording"}]];
        return NO;
    }
}

- (void)pauseRecording {
    if (self.state == RecordingStateRecording) {
        [self.audioRecorder pause];
        self.state = RecordingStatePaused;
        [self stopTimer];
        [self.delegate recordingStateDidChange:self.state];
    }
}

- (void)resumeRecording {
    if (self.state == RecordingStatePaused) {
        [self.audioRecorder record];
        self.state = RecordingStateRecording;
        [self startTimer];
        [self.delegate recordingStateDidChange:self.state];
    }
}

- (void)stopRecording {
    if (self.state == RecordingStateRecording || self.state == RecordingStatePaused) {
        [self.audioRecorder stop];
        [self stopTimer];
        self.state = RecordingStateStopped;
        [self.delegate recordingStateDidChange:self.state];
        [self.delegate recordingDidFinishWithURL:self.currentRecordingURL];
        
        NSError *error = nil;
        [self.audioSession setActive:NO error:&error];
    }
}

- (void)cancelRecording {
    if (self.state == RecordingStateRecording || self.state == RecordingStatePaused) {
        [self.audioRecorder stop];
        [self stopTimer];
        
        // Delete the recording file
        if (self.currentRecordingURL) {
            [[NSFileManager defaultManager] removeItemAtURL:self.currentRecordingURL error:nil];
        }
        
        self.state = RecordingStateStopped;
        self.currentTime = 0.0;
        [self.delegate recordingStateDidChange:self.state];
        
        NSError *error = nil;
        [self.audioSession setActive:NO error:&error];
    }
}

- (void)startTimer {
    self.timer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(updateTime) userInfo:nil repeats:YES];
}

- (void)stopTimer {
    [self.timer invalidate];
    self.timer = nil;
}

- (void)updateTime {
    if (self.audioRecorder && self.audioRecorder.isRecording) {
        self.currentTime = self.audioRecorder.currentTime;
        [self.delegate recordingTimeDidUpdate:self.currentTime];
    }
}

- (NSArray<NSURL *> *)getAllRecordings {
    NSError *error = nil;
    NSURL *documentsURL = [[[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
    NSArray *contents = [[NSFileManager defaultManager] contentsOfDirectoryAtURL:documentsURL includingPropertiesForKeys:@[NSURLCreationDateKey] options:NSDirectoryEnumerationSkipsHiddenFiles error:&error];
    
    if (error) {
        return @[];
    }
    
    NSMutableArray *recordings = [NSMutableArray array];
    for (NSURL *url in contents) {
        if ([url.pathExtension isEqualToString:@"m4a"]) {
            [recordings addObject:url];
        }
    }
    
    // Sort by creation date (newest first)
    [recordings sortUsingComparator:^NSComparisonResult(NSURL *url1, NSURL *url2) {
        NSDate *date1, *date2;
        [url1 getResourceValue:&date1 forKey:NSURLCreationDateKey error:nil];
        [url2 getResourceValue:&date2 forKey:NSURLCreationDateKey error:nil];
        return [date2 compare:date1];
    }];
    
    return [recordings copy];
}

- (BOOL)deleteRecordingAtURL:(NSURL *)url {
    NSError *error = nil;
    BOOL success = [[NSFileManager defaultManager] removeItemAtURL:url error:&error];
    if (!success) {
        NSLog(@"Error deleting recording: %@", error.localizedDescription);
    }
    return success;
}

- (NSString *)formatTime:(NSTimeInterval)time {
    int minutes = (int)time / 60;
    int seconds = (int)time % 60;
    int milliseconds = (int)((time - floor(time)) * 100);
    return [NSString stringWithFormat:@"%02d:%02d.%02d", minutes, seconds, milliseconds];
}

#pragma mark - AVAudioRecorderDelegate

- (void)audioRecorderDidFinishRecording:(AVAudioRecorder *)recorder successfully:(BOOL)flag {
    if (flag) {
        [self.delegate recordingDidFinishWithURL:self.currentRecordingURL];
    } else {
        [self.delegate recordingDidFailWithError:[NSError errorWithDomain:@"RecordingError" code:2 userInfo:@{NSLocalizedDescriptionKey: @"Recording failed"}]];
    }
}

- (void)audioRecorderEncodeErrorDidOccur:(AVAudioRecorder *)recorder error:(NSError *)error {
    [self.delegate recordingDidFailWithError:error];
}

@end
