//
//  SMSListInterfaceController.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <WatchKit/WatchKit.h>
#import <Foundation/Foundation.h>
#import "SMSNotificationService.h"

@interface SMSListInterfaceController : WKInterfaceController <SMSNotificationServiceDelegate>

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceTable *smsTable;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *statusLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *clearAllButton;

- (IBAction)clearAllButtonTapped;
- (IBAction)addTestSMSButtonTapped;

@end
