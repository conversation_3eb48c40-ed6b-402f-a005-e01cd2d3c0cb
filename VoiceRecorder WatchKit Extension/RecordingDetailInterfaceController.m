//
//  RecordingDetailInterfaceController.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "RecordingDetailInterfaceController.h"
#import "RecordingManager.h"

@interface RecordingDetailInterfaceController () <AVAudioPlayerDelegate>
@property (nonatomic, strong) NSURL *recordingURL;
@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, strong) NSTimer *progressTimer;
@property (nonatomic, assign) BOOL isPlaying;
@end

@implementation RecordingDetailInterfaceController

- (void)awakeWithContext:(id)context {
    [super awakeWithContext:context];
    
    // Configure interface objects here.
    if ([context isKindOfClass:[NSURL class]]) {
        self.recordingURL = (NSURL *)context;
        [self setupInterface];
    }
}

- (void)willActivate {
    // This method is called when watch view controller is about to be visible to user
    [super willActivate];
}

- (void)didDeactivate {
    // This method is called when watch view controller is no longer visible
    [super didDeactivate];
    [self stopPlayback];
}

- (void)setupInterface {
    // Set title
    NSString *fileName = [self.recordingURL.lastPathComponent stringByDeletingPathExtension];
    NSString *displayName = fileName;
    if ([fileName hasPrefix:@"Recording_"]) {
        NSString *dateString = [fileName substringFromIndex:10];
        displayName = [self formatDateString:dateString];
    }
    [self.titleLabel setText:displayName];
    
    // Setup audio player
    NSError *error;
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:self.recordingURL error:&error];
    if (error) {
        NSLog(@"Error creating audio player: %@", error.localizedDescription);
        return;
    }
    
    self.audioPlayer.delegate = self;
    [self.audioPlayer prepareToPlay];
    
    // Set duration
    NSTimeInterval duration = self.audioPlayer.duration;
    NSString *durationString = [[RecordingManager sharedManager] formatTime:duration];
    [self.durationLabel setText:durationString];
    
    // Setup progress slider
    [self.progressSlider setMaximumValue:duration];
    [self.progressSlider setValue:0.0];
    
    // Setup buttons
    [self updatePlayButton];
}

- (void)updatePlayButton {
    if (self.isPlaying) {
        [self.playButton setTitle:@"⏸"];
        [self.playButton setBackgroundColor:[UIColor orangeColor]];
    } else {
        [self.playButton setTitle:@"▶"];
        [self.playButton setBackgroundColor:[UIColor greenColor]];
    }
}

- (NSString *)formatDateString:(NSString *)dateString {
    NSDateFormatter *inputFormatter = [[NSDateFormatter alloc] init];
    inputFormatter.dateFormat = @"yyyy-MM-dd_HH-mm-ss";
    
    NSDate *date = [inputFormatter dateFromString:dateString];
    if (!date) {
        return dateString;
    }
    
    NSDateFormatter *outputFormatter = [[NSDateFormatter alloc] init];
    outputFormatter.dateStyle = NSDateFormatterShortStyle;
    outputFormatter.timeStyle = NSDateFormatterShortStyle;
    
    return [outputFormatter stringFromDate:date];
}

- (IBAction)playButtonTapped {
    if (self.isPlaying) {
        [self pausePlayback];
    } else {
        [self startPlayback];
    }
}

- (IBAction)deleteButtonTapped {
    WKAlertAction *deleteAction = [WKAlertAction actionWithTitle:@"Delete" style:WKAlertActionStyleDestructive handler:^{
        BOOL success = [[RecordingManager sharedManager] deleteRecordingAtURL:self.recordingURL];
        if (success) {
            [self popController];
        } else {
            [self presentAlertControllerWithTitle:@"Error" 
                                          message:@"Failed to delete recording." 
                                   preferredStyle:WKAlertControllerStyleAlert 
                                          actions:@[[WKAlertAction actionWithTitle:@"OK" style:WKAlertActionStyleDefault handler:nil]]];
        }
    }];
    
    WKAlertAction *cancelAction = [WKAlertAction actionWithTitle:@"Cancel" style:WKAlertActionStyleCancel handler:nil];
    
    [self presentAlertControllerWithTitle:@"Delete Recording" 
                                  message:@"Are you sure you want to delete this recording?" 
                           preferredStyle:WKAlertControllerStyleAlert 
                                  actions:@[deleteAction, cancelAction]];
}

- (void)startPlayback {
    NSError *error;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
    [[AVAudioSession sharedInstance] setActive:YES error:&error];
    
    [self.audioPlayer play];
    self.isPlaying = YES;
    [self updatePlayButton];
    [self startProgressTimer];
}

- (void)pausePlayback {
    [self.audioPlayer pause];
    self.isPlaying = NO;
    [self updatePlayButton];
    [self stopProgressTimer];
}

- (void)stopPlayback {
    [self.audioPlayer stop];
    self.audioPlayer.currentTime = 0;
    self.isPlaying = NO;
    [self updatePlayButton];
    [self stopProgressTimer];
    [self.progressSlider setValue:0.0];
}

- (void)startProgressTimer {
    self.progressTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(updateProgress) userInfo:nil repeats:YES];
}

- (void)stopProgressTimer {
    [self.progressTimer invalidate];
    self.progressTimer = nil;
}

- (void)updateProgress {
    if (self.audioPlayer) {
        [self.progressSlider setValue:self.audioPlayer.currentTime];
    }
}

#pragma mark - AVAudioPlayerDelegate

- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    self.isPlaying = NO;
    [self updatePlayButton];
    [self stopProgressTimer];
    [self.progressSlider setValue:0.0];
    self.audioPlayer.currentTime = 0;
}

@end
