//
//  SMSNotificationService.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <Foundation/Foundation.h>
#import <UserNotifications/UserNotifications.h>
#import "SMSMessage.h"

@protocol SMSNotificationServiceDelegate <NSObject>
- (void)didReceiveNewSMSMessage:(SMSMessage *)message;
- (void)smsNotificationServiceDidFailWithError:(NSError *)error;
@end

@interface SMSNotificationService : NSObject <UNUserNotificationCenterDelegate>

@property (nonatomic, weak) id<SMSNotificationServiceDelegate> delegate;
@property (nonatomic, readonly) BOOL isMonitoring;

+ (instancetype)sharedService;

- (void)startMonitoring;
- (void)stopMonitoring;
- (void)requestNotificationPermissions;

// Manual SMS addition (for testing or manual input)
- (void)addManualSMS:(NSString *)content fromSender:(NSString *)sender senderName:(NSString *)senderName;

@end
