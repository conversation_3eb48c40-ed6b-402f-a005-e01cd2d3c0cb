//
//  InterfaceController.h
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import <WatchKit/WatchKit.h>
#import <Foundation/Foundation.h>
#import "RecordingManager.h"

@interface InterfaceController : WKInterfaceController <RecordingManagerDelegate>

@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceLabel *timeLabel;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *recordButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *pauseButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *stopButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceButton *listButton;
@property (unsafe_unretained, nonatomic) IBOutlet WKInterfaceGroup *waveformGroup;

- (IBAction)recordButtonTapped;
- (IBAction)pauseButtonTapped;
- (IBAction)stopButtonTapped;
- (IBAction)listButtonTapped;

@end
