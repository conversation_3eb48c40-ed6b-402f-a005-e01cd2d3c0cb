//
//  SMSDataManager.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "SMSDataManager.h"

@interface SMSDataManager ()
@property (nonatomic, strong) NSMutableArray<SMSMessage *> *messages;
@property (nonatomic, strong) NSURL *dataFileURL;
@property (nonatomic, strong) dispatch_queue_t dataQueue;
@end

@implementation SMSDataManager

+ (instancetype)sharedManager {
    static SMSDataManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[SMSDataManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _messages = [NSMutableArray array];
        _dataQueue = dispatch_queue_create("com.voicerecorder.sms.data", DISPATCH_QUEUE_SERIAL);
        
        // Setup data file URL
        NSURL *documentsURL = [[[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
        _dataFileURL = [documentsURL URLByAppendingPathComponent:@"sms_messages.plist"];
        
        [self loadMessages];
    }
    return self;
}

- (void)loadMessages {
    dispatch_async(self.dataQueue, ^{
        if ([[NSFileManager defaultManager] fileExistsAtPath:self.dataFileURL.path]) {
            NSError *error;
            NSData *data = [NSData dataWithContentsOfURL:self.dataFileURL options:0 error:&error];
            if (data && !error) {
                NSArray *messagesDictArray = [NSPropertyListSerialization propertyListWithData:data options:0 format:nil error:&error];
                if (messagesDictArray && !error) {
                    NSMutableArray *loadedMessages = [NSMutableArray array];
                    for (NSDictionary *messageDict in messagesDictArray) {
                        SMSMessage *message = [SMSMessage fromDictionary:messageDict];
                        if (message) {
                            [loadedMessages addObject:message];
                        }
                    }
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        self.messages = loadedMessages;
                    });
                }
            }
        }
    });
}

- (void)saveMessages {
    dispatch_async(self.dataQueue, ^{
        NSMutableArray *messagesDictArray = [NSMutableArray array];
        for (SMSMessage *message in self.messages) {
            [messagesDictArray addObject:[message toDictionary]];
        }
        
        NSError *error;
        NSData *data = [NSPropertyListSerialization dataWithPropertyList:messagesDictArray format:NSPropertyListXMLFormat_v1_0 options:0 error:&error];
        if (data && !error) {
            [data writeToURL:self.dataFileURL options:NSDataWritingAtomic error:&error];
            if (error) {
                NSLog(@"Error saving SMS messages: %@", error.localizedDescription);
            }
        }
    });
}

- (void)saveMessage:(SMSMessage *)message {
    if (!message) return;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        // Check if message already exists
        NSInteger existingIndex = -1;
        for (NSInteger i = 0; i < self.messages.count; i++) {
            if ([self.messages[i].messageID isEqualToString:message.messageID]) {
                existingIndex = i;
                break;
            }
        }
        
        if (existingIndex >= 0) {
            // Update existing message
            self.messages[existingIndex] = message;
        } else {
            // Add new message
            [self.messages addObject:message];
            
            // Sort messages by timestamp (newest first)
            [self.messages sortUsingComparator:^NSComparisonResult(SMSMessage *msg1, SMSMessage *msg2) {
                return [msg2.timestamp compare:msg1.timestamp];
            }];
        }
        
        [self saveMessages];
    });
}

- (NSArray<SMSMessage *> *)getAllMessages {
    return [self.messages copy];
}

- (NSArray<SMSMessage *> *)getMessagesFromSender:(NSString *)sender {
    if (!sender) return @[];
    
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"sender == %@", sender];
    return [self.messages filteredArrayUsingPredicate:predicate];
}

- (NSArray<SMSMessage *> *)getUnreadMessages {
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"isRead == NO"];
    return [self.messages filteredArrayUsingPredicate:predicate];
}

- (SMSMessage *)getMessageByID:(NSString *)messageID {
    if (!messageID) return nil;
    
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"messageID == %@", messageID];
    NSArray *results = [self.messages filteredArrayUsingPredicate:predicate];
    return results.firstObject;
}

- (void)markMessageAsRead:(NSString *)messageID {
    SMSMessage *message = [self getMessageByID:messageID];
    if (message) {
        message.isRead = YES;
        [self saveMessages];
    }
}

- (void)deleteMessage:(NSString *)messageID {
    if (!messageID) return;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        NSInteger indexToRemove = -1;
        for (NSInteger i = 0; i < self.messages.count; i++) {
            if ([self.messages[i].messageID isEqualToString:messageID]) {
                indexToRemove = i;
                break;
            }
        }
        
        if (indexToRemove >= 0) {
            [self.messages removeObjectAtIndex:indexToRemove];
            [self saveMessages];
        }
    });
}

- (void)deleteAllMessages {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.messages removeAllObjects];
        [self saveMessages];
    });
}

- (NSInteger)getTotalMessageCount {
    return self.messages.count;
}

- (NSInteger)getUnreadMessageCount {
    return [self getUnreadMessages].count;
}

- (NSArray<NSString *> *)getAllSenders {
    NSMutableSet *sendersSet = [NSMutableSet set];
    for (SMSMessage *message in self.messages) {
        if (message.sender) {
            [sendersSet addObject:message.sender];
        }
    }
    return [sendersSet.allObjects sortedArrayUsingSelector:@selector(compare:)];
}

- (BOOL)exportMessagesToFile:(NSURL *)fileURL {
    if (!fileURL) return NO;
    
    NSMutableArray *messagesDictArray = [NSMutableArray array];
    for (SMSMessage *message in self.messages) {
        [messagesDictArray addObject:[message toDictionary]];
    }
    
    NSError *error;
    NSData *data = [NSPropertyListSerialization dataWithPropertyList:messagesDictArray format:NSPropertyListXMLFormat_v1_0 options:0 error:&error];
    if (data && !error) {
        BOOL success = [data writeToURL:fileURL options:NSDataWritingAtomic error:&error];
        if (!success) {
            NSLog(@"Error exporting SMS messages: %@", error.localizedDescription);
        }
        return success;
    }
    
    return NO;
}

- (BOOL)importMessagesFromFile:(NSURL *)fileURL {
    if (!fileURL || ![[NSFileManager defaultManager] fileExistsAtPath:fileURL.path]) {
        return NO;
    }
    
    NSError *error;
    NSData *data = [NSData dataWithContentsOfURL:fileURL options:0 error:&error];
    if (data && !error) {
        NSArray *messagesDictArray = [NSPropertyListSerialization propertyListWithData:data options:0 format:nil error:&error];
        if (messagesDictArray && !error) {
            for (NSDictionary *messageDict in messagesDictArray) {
                SMSMessage *message = [SMSMessage fromDictionary:messageDict];
                if (message) {
                    [self saveMessage:message];
                }
            }
            return YES;
        }
    }
    
    return NO;
}

@end
