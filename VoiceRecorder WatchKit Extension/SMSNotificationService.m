//
//  SMSNotificationService.m
//  VoiceRecorder WatchKit Extension
//
//  Created by AI Assistant on 2025-06-21.
//

#import "SMSNotificationService.h"
#import "SMSDataManager.h"
#import <WatchKit/WatchKit.h>

@interface SMSNotificationService ()
@property (nonatomic, assign) BOOL isMonitoring;
@property (nonatomic, strong) UNUserNotificationCenter *notificationCenter;
@end

@implementation SMSNotificationService

+ (instancetype)sharedService {
    static SMSNotificationService *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[SMSNotificationService alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _notificationCenter = [UNUserNotificationCenter currentNotificationCenter];
        _isMonitoring = NO;
    }
    return self;
}

- (void)requestNotificationPermissions {
    UNAuthorizationOptions options = UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge;
    
    [self.notificationCenter requestAuthorizationWithOptions:options completionHandler:^(BOOL granted, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (granted) {
                NSLog(@"Notification permissions granted");
                [self startMonitoring];
            } else {
                NSLog(@"Notification permissions denied");
                if (self.delegate) {
                    NSError *permissionError = [NSError errorWithDomain:@"SMSNotificationService" 
                                                                   code:1001 
                                                               userInfo:@{NSLocalizedDescriptionKey: @"Notification permissions denied"}];
                    [self.delegate smsNotificationServiceDidFailWithError:permissionError];
                }
            }
        });
    }];
}

- (void)startMonitoring {
    if (self.isMonitoring) {
        return;
    }
    
    self.notificationCenter.delegate = self;
    self.isMonitoring = YES;
    
    NSLog(@"SMS notification monitoring started");
    
    // Note: On Apple Watch, we can't directly intercept SMS notifications
    // This is a limitation of watchOS. We can only receive notifications
    // that are forwarded from the iPhone or manually created.
    
    // For demonstration, we'll create some sample SMS messages
    [self createSampleMessages];
}

- (void)stopMonitoring {
    if (!self.isMonitoring) {
        return;
    }
    
    self.notificationCenter.delegate = nil;
    self.isMonitoring = NO;
    
    NSLog(@"SMS notification monitoring stopped");
}

- (void)createSampleMessages {
    // Create some sample SMS messages for demonstration
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self addManualSMS:@"Hello! How are you doing today?" 
                fromSender:@"+1234567890" 
                senderName:@"John Doe"];
    });
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self addManualSMS:@"Don't forget about our meeting tomorrow at 3 PM." 
                fromSender:@"+0987654321" 
                senderName:@"Jane Smith"];
    });
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(8.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self addManualSMS:@"Your package has been delivered!" 
                fromSender:@"+1122334455" 
                senderName:@"Delivery Service"];
    });
}

- (void)addManualSMS:(NSString *)content fromSender:(NSString *)sender senderName:(NSString *)senderName {
    if (!content || !sender) {
        return;
    }
    
    SMSMessage *message = [[SMSMessage alloc] initWithContent:content 
                                                       sender:sender 
                                                   senderName:senderName 
                                                    timestamp:[NSDate date]];
    
    // Save to data manager
    [[SMSDataManager sharedManager] saveMessage:message];
    
    // Notify delegate
    if (self.delegate) {
        [self.delegate didReceiveNewSMSMessage:message];
    }
    
    // Create local notification for the watch
    [self createLocalNotificationForMessage:message];
    
    NSLog(@"Manual SMS added: %@", message);
}

- (void)createLocalNotificationForMessage:(SMSMessage *)message {
    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    content.title = [NSString stringWithFormat:@"SMS from %@", message.senderName ?: message.sender];
    content.body = message.content;
    content.sound = [UNNotificationSound defaultSound];
    content.userInfo = @{
        @"messageID": message.messageID,
        @"sender": message.sender,
        @"type": @"sms"
    };
    
    // Create trigger (immediate)
    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO];
    
    // Create request
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:message.messageID 
                                                                          content:content 
                                                                          trigger:trigger];
    
    // Add notification
    [self.notificationCenter addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            NSLog(@"Error creating notification: %@", error.localizedDescription);
        }
    }];
}

#pragma mark - UNUserNotificationCenterDelegate

- (void)userNotificationCenter:(UNUserNotificationCenter *)center 
       willPresentNotification:(UNNotification *)notification 
         withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
    
    // Handle notification when app is in foreground
    NSDictionary *userInfo = notification.request.content.userInfo;
    NSString *type = userInfo[@"type"];
    
    if ([type isEqualToString:@"sms"]) {
        // This is our SMS notification
        completionHandler(UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound);
    } else {
        // Check if this might be an SMS from the system
        [self processPotentialSMSNotification:notification];
        completionHandler(UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound);
    }
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center 
didReceiveNotificationResponse:(UNNotificationResponse *)response 
         withCompletionHandler:(void (^)(void))completionHandler {
    
    // Handle notification tap
    NSDictionary *userInfo = response.notification.request.content.userInfo;
    NSString *messageID = userInfo[@"messageID"];
    
    if (messageID) {
        // Mark message as read
        [[SMSDataManager sharedManager] markMessageAsRead:messageID];
    }
    
    completionHandler();
}

- (void)processPotentialSMSNotification:(UNNotification *)notification {
    // Try to extract SMS information from system notifications
    // This is limited on watchOS, but we can try to parse the content
    
    NSString *title = notification.request.content.title;
    NSString *body = notification.request.content.body;
    NSDictionary *userInfo = notification.request.content.userInfo;
    
    // Look for SMS-like patterns
    if ([title containsString:@"Message"] || [title containsString:@"SMS"] || 
        [userInfo[@"aps"][@"category"] isEqualToString:@"MessageExtension"]) {
        
        // Extract sender information (this is very limited)
        NSString *sender = @"Unknown";
        NSString *senderName = title;
        
        // Try to extract phone number or contact name from title
        NSRegularExpression *phoneRegex = [NSRegularExpression regularExpressionWithPattern:@"\\+?[0-9\\-\\s\\(\\)]+" 
                                                                                     options:0 
                                                                                       error:nil];
        NSTextCheckingResult *match = [phoneRegex firstMatchInString:title 
                                                             options:0 
                                                               range:NSMakeRange(0, title.length)];
        if (match) {
            sender = [title substringWithRange:match.range];
        }
        
        // Create SMS message
        SMSMessage *message = [[SMSMessage alloc] initWithContent:body 
                                                           sender:sender 
                                                       senderName:senderName 
                                                        timestamp:[NSDate date]];
        
        // Save and notify
        [[SMSDataManager sharedManager] saveMessage:message];
        
        if (self.delegate) {
            [self.delegate didReceiveNewSMSMessage:message];
        }
        
        NSLog(@"Processed potential SMS: %@", message);
    }
}

@end
