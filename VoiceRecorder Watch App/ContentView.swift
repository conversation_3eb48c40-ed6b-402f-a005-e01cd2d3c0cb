//
//  ContentView.swift
//  VoiceRecorder Watch App
//
//  Created by 唐君华 on 2025/6/21.
//

import SwiftUI
import WatchKit
import AVFoundation
import Messages
import MessageUI

struct ContentView: View {
    @State private var isRecording = false
    @State private var isPaused = false
    @State private var recordingTime = "00:00"
    @State private var smsCount = 3
    @State private var showingRecordingsList = false
    @State private var showingSMSList = false
    @State private var recordingSeconds = 0
    @State private var timer: Timer?
    @StateObject private var audioRecorder = AudioRecorder()
    @State private var showingPermissionAlert = false

    var body: some View {
        VStack(spacing: 8) {
            // 时间显示
            Text(recordingTime)
                .font(.title2)
                .foregroundColor(.white)
                .monospacedDigit()

            // 录音控制按钮
            HStack(spacing: 15) {
                // 录音/停止按钮
                Button(action: {
                    if isRecording {
                        stopRecording()
                    } else {
                        startRecording()
                    }
                }) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "record.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(isRecording ? .red : .green)
                }
                .buttonStyle(PlainButtonStyle())

                // 暂停按钮（仅在录音时显示）
                if isRecording {
                    Button(action: {
                        pauseRecording()
                    }) {
                        Image(systemName: isPaused ? "play.circle.fill" : "pause.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.orange)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }

            // 功能按钮
            HStack(spacing: 20) {
                // 录音列表按钮
                Button(action: {
                    showingRecordingsList = true
                }) {
                    VStack(spacing: 2) {
                        Image(systemName: "list.bullet")
                            .font(.title3)
                        Text("录音")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.blue)

                // 短信按钮
                Button(action: {
                    showingSMSList = true
                }) {
                    VStack(spacing: 2) {
                        ZStack {
                            Image(systemName: "message.fill")
                                .font(.title3)
                            if smsCount > 0 {
                                Text("\(smsCount)")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(width: 16, height: 16)
                                    .background(Circle().fill(Color.red))
                                    .offset(x: 8, y: -8)
                            }
                        }
                        Text("短信")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.green)
            }
        }
        .padding()
        .sheet(isPresented: $showingRecordingsList) {
            RecordingsListView()
        }
        .sheet(isPresented: $showingSMSList) {
            SMSListView(smsCount: $smsCount)
        }
    }

    func startRecording() {
        isRecording = true
        isPaused = false
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.start)

        // 开始计时
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if !isPaused {
                recordingSeconds += 1
                updateRecordingTime()
            }
        }
    }

    func stopRecording() {
        isRecording = false
        isPaused = false
        timer?.invalidate()
        timer = nil
        recordingTime = "00:00"
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.stop)
    }

    func pauseRecording() {
        isPaused.toggle()
        WKInterfaceDevice.current().play(.click)
    }

    func updateRecordingTime() {
        let minutes = recordingSeconds / 60
        let seconds = recordingSeconds % 60
        recordingTime = String(format: "%02d:%02d", minutes, seconds)
    }
}

struct RecordingsListView: View {
    @Environment(\.dismiss) private var dismiss

    let recordings = [
        "录音 001 - 2分30秒",
        "录音 002 - 1分15秒",
        "录音 003 - 3分45秒"
    ]

    var body: some View {
        NavigationView {
            List(recordings, id: \.self) { recording in
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(.blue)
                    Text(recording)
                        .font(.caption)
                }
            }
            .navigationTitle("录音列表")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct SMSListView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var smsCount: Int
    @State private var messages: [SMSMessage] = []
    @State private var isLoading = true
    @State private var errorMessage: String?

    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    VStack {
                        ProgressView()
                        Text("读取短信中...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else if let error = errorMessage {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.title2)
                            .foregroundColor(.orange)
                        Text(error)
                            .font(.caption)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else if messages.isEmpty {
                    VStack {
                        Image(systemName: "message")
                            .font(.title2)
                            .foregroundColor(.secondary)
                        Text("暂无短信")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else {
                    List(messages) { message in
                        VStack(alignment: .leading, spacing: 2) {
                            HStack {
                                Text(message.sender)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                Spacer()
                                Text(message.time)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            Text(message.content)
                                .font(.caption2)
                                .lineLimit(3)
                        }
                        .onTapGesture {
                            if smsCount > 0 {
                                smsCount -= 1
                            }
                        }
                    }
                }
            }
            .navigationTitle("短信")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            loadSystemMessages()
        }
    }

    private func loadSystemMessages() {
        isLoading = true
        errorMessage = nil

        // 使用MessageStore读取系统短信
        SMSReader.shared.readMessages { result in
            DispatchQueue.main.async {
                isLoading = false
                switch result {
                case .success(let smsMessages):
                    self.messages = smsMessages
                    self.smsCount = smsMessages.filter { !$0.isRead }.count
                case .failure(let error):
                    self.errorMessage = error.localizedDescription
                    self.messages = []
                }
            }
        }
    }
}

struct SMSMessage: Identifiable {
    let id = UUID()
    let sender: String
    let content: String
    let time: String
    let isRead: Bool
    let timestamp: Date

    init(sender: String, content: String, time: String, isRead: Bool = true, timestamp: Date = Date()) {
        self.id = UUID()
        self.sender = sender
        self.content = content
        self.time = time
        self.isRead = isRead
        self.timestamp = timestamp
    }
}

// SMS读取器类
class SMSReader: ObservableObject {
    static let shared = SMSReader()

    private init() {}

    func readMessages(completion: @escaping (Result<[SMSMessage], Error>) -> Void) {
        // 在watchOS中，我们需要通过iPhone应用来获取短信数据
        // 这里我们模拟从系统读取短信的过程

        DispatchQueue.global(qos: .background).async {
            // 模拟网络延迟
            Thread.sleep(forTimeInterval: 1.0)

            // 在实际应用中，这里应该通过以下方式获取短信：
            // 1. 通过WatchConnectivity从iPhone获取
            // 2. 或者通过CloudKit同步
            // 3. 或者通过自定义的后端API

            let sampleMessages = self.getSampleSystemMessages()

            DispatchQueue.main.async {
                completion(.success(sampleMessages))
            }
        }
    }

    private func getSampleSystemMessages() -> [SMSMessage] {
        // 这里模拟从系统读取的真实短信数据
        // 在实际应用中，需要通过适当的API获取
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"

        let now = Date()
        let messages = [
            SMSMessage(
                sender: "10086",
                content: "您的话费余额为68.5元，流量剩余2.1GB。",
                time: formatter.string(from: now.addingTimeInterval(-3600)),
                isRead: false,
                timestamp: now.addingTimeInterval(-3600)
            ),
            SMSMessage(
                sender: "95588",
                content: "您的银行卡尾号1234于今日15:30消费200.00元。",
                time: formatter.string(from: now.addingTimeInterval(-7200)),
                isRead: true,
                timestamp: now.addingTimeInterval(-7200)
            ),
            SMSMessage(
                sender: "张三",
                content: "明天的会议改到下午3点，地点不变。",
                time: formatter.string(from: now.addingTimeInterval(-10800)),
                isRead: false,
                timestamp: now.addingTimeInterval(-10800)
            ),
            SMSMessage(
                sender: "快递小哥",
                content: "您的快递已到达小区门口，请及时取件。",
                time: formatter.string(from: now.addingTimeInterval(-14400)),
                isRead: true,
                timestamp: now.addingTimeInterval(-14400)
            )
        ]

        return messages.sorted { $0.timestamp > $1.timestamp }
    }
}

#Preview {
    ContentView()
}
