//
//  ContentView.swift
//  VoiceRecorder Watch App
//
//  Created by 唐君华 on 2025/6/21.
//

import SwiftUI
import WatchKit
import AVFoundation
import Foundation

struct ContentView: View {
    @State private var isRecording = false
    @State private var isPaused = false
    @State private var recordingTime = "00:00"
    @State private var showingRecordingsList = false
    @State private var showingSMSList = false
    @State private var recordingSeconds = 0
    @State private var timer: Timer?
    @StateObject private var audioRecorder = AudioRecorder()
    @StateObject private var smsReader = SMSReader()
    @State private var showingPermissionAlert = false

    var body: some View {
        VStack(spacing: 8) {
            // 时间显示
            Text(recordingTime)
                .font(.title2)
                .foregroundColor(.white)
                .monospacedDigit()

            // 录音控制按钮
            HStack(spacing: 15) {
                // 录音/停止按钮
                Button(action: {
                    if isRecording {
                        stopRecording()
                    } else {
                        startRecording()
                    }
                }) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "record.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(isRecording ? .red : .green)
                }
                .buttonStyle(PlainButtonStyle())

                // 暂停按钮（仅在录音时显示）
                if isRecording {
                    Button(action: {
                        pauseRecording()
                    }) {
                        Image(systemName: isPaused ? "play.circle.fill" : "pause.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.orange)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }

            // 功能按钮
            HStack(spacing: 20) {
                // 录音列表按钮
                Button(action: {
                    showingRecordingsList = true
                }) {
                    VStack(spacing: 2) {
                        Image(systemName: "list.bullet")
                            .font(.title3)
                        Text("录音")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.blue)

                // 短信按钮
                Button(action: {
                    showingSMSList = true
                }) {
                    VStack(spacing: 2) {
                        ZStack {
                            Image(systemName: "message.fill")
                                .font(.title3)
                            if smsReader.unreadCount > 0 {
                                Text("\(smsReader.unreadCount)")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(width: 16, height: 16)
                                    .background(Circle().fill(Color.red))
                                    .offset(x: 8, y: -8)
                            }
                        }
                        Text("短信")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.green)
            }
        }
        .padding()
        .sheet(isPresented: $showingRecordingsList) {
            RecordingsListView(audioRecorder: audioRecorder)
        }
        .sheet(isPresented: $showingSMSList) {
            SMSListView(smsReader: smsReader)
        }
        .alert("需要录音权限", isPresented: $showingPermissionAlert) {
            Button("确定") { }
        } message: {
            Text("请在iPhone上的Watch应用中允许录音权限")
        }
    }

    func startRecording() {
        audioRecorder.requestPermission { granted in
            DispatchQueue.main.async {
                if granted {
                    self.beginRecording()
                } else {
                    self.showingPermissionAlert = true
                }
            }
        }
    }

    private func beginRecording() {
        isRecording = true
        isPaused = false
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.start)

        audioRecorder.startRecording()

        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if !isPaused {
                recordingSeconds += 1
                updateRecordingTime()
            }
        }
    }

    func stopRecording() {
        isRecording = false
        isPaused = false
        timer?.invalidate()
        timer = nil
        recordingTime = "00:00"
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.stop)

        audioRecorder.stopRecording()
    }

    func pauseRecording() {
        isPaused.toggle()
        WKInterfaceDevice.current().play(.click)

        if isPaused {
            audioRecorder.pauseRecording()
        } else {
            audioRecorder.resumeRecording()
        }
    }

    func updateRecordingTime() {
        let minutes = recordingSeconds / 60
        let seconds = recordingSeconds % 60
        recordingTime = String(format: "%02d:%02d", minutes, seconds)
    }
}

struct RecordingsListView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var audioRecorder: AudioRecorder

    var body: some View {
        NavigationView {
            Group {
                if audioRecorder.recordings.isEmpty {
                    VStack {
                        Image(systemName: "waveform.slash")
                            .font(.title2)
                            .foregroundColor(.secondary)
                        Text("暂无录音")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else {
                    List {
                        ForEach(audioRecorder.recordings) { recording in
                            RecordingRowView(recording: recording, audioRecorder: audioRecorder)
                        }
                        .onDelete(perform: deleteRecordings)
                    }
                }
            }
            .navigationTitle("录音列表")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    func deleteRecordings(at offsets: IndexSet) {
        for index in offsets {
            audioRecorder.deleteRecording(audioRecorder.recordings[index])
        }
    }
}

struct SMSListView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var smsReader: SMSReader

    var body: some View {
        NavigationView {
            Group {
                if smsReader.isLoading {
                    VStack {
                        ProgressView()
                        Text("读取短信中...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else if let error = smsReader.errorMessage {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.title2)
                            .foregroundColor(.orange)
                        Text(error)
                            .font(.caption)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else if smsReader.messages.isEmpty {
                    VStack {
                        Image(systemName: "message")
                            .font(.title2)
                            .foregroundColor(.secondary)
                        Text("暂无短信")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else {
                    List(smsReader.messages) { message in
                        VStack(alignment: .leading, spacing: 2) {
                            HStack {
                                Text(message.sender)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                Spacer()
                                Text(message.time)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            Text(message.content)
                                .font(.caption2)
                                .lineLimit(3)
                        }
                        .onTapGesture {
                            smsReader.markAsRead(message)
                        }
                    }
                }
            }
            .navigationTitle("短信")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            smsReader.loadMessages()
        }
    }
}

// 录音数据模型
struct Recording: Identifiable {
    let id = UUID()
    let url: URL
    let name: String
    let duration: TimeInterval
    let createdAt: Date

    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd HH:mm"
        return formatter.string(from: createdAt)
    }
}

// 录音行视图
struct RecordingRowView: View {
    let recording: Recording
    @ObservedObject var audioRecorder: AudioRecorder
    @State private var isPlaying = false

    var body: some View {
        HStack {
            Button(action: {
                if isPlaying {
                    audioRecorder.stopPlayback()
                } else {
                    audioRecorder.playRecording(recording)
                }
                isPlaying.toggle()
            }) {
                Image(systemName: isPlaying ? "stop.circle.fill" : "play.circle.fill")
                    .foregroundColor(.blue)
                    .font(.title3)
            }
            .buttonStyle(PlainButtonStyle())

            VStack(alignment: .leading, spacing: 2) {
                Text(recording.name)
                    .font(.caption)
                    .fontWeight(.semibold)
                HStack {
                    Text(recording.formattedDuration)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(recording.formattedDate)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

// 短信数据模型
struct SMSMessage: Identifiable {
    let id = UUID()
    let sender: String
    let content: String
    let time: String
    var isRead: Bool
    let timestamp: Date
}

// 真实的录音器类
class AudioRecorder: NSObject, ObservableObject {
    @Published var recordings: [Recording] = []
    @Published var isRecording = false

    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private var recordingSession: AVAudioSession!

    override init() {
        super.init()
        setupRecordingSession()
        loadRecordings()
    }

    func setupRecordingSession() {
        recordingSession = AVAudioSession.sharedInstance()

        do {
            try recordingSession.setCategory(.playAndRecord, mode: .default)
            try recordingSession.setActive(true)
        } catch {
            print("录音会话设置失败: \(error)")
        }
    }

    func requestPermission(completion: @escaping (Bool) -> Void) {
        recordingSession.requestRecordPermission { allowed in
            completion(allowed)
        }
    }

    func startRecording() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = "录音_\(Date().timeIntervalSince1970).m4a"
        let audioURL = documentsPath.appendingPathComponent(fileName)

        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 12000,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]

        do {
            audioRecorder = try AVAudioRecorder(url: audioURL, settings: settings)
            audioRecorder?.record()
            isRecording = true
        } catch {
            print("录音开始失败: \(error)")
        }
    }

    func stopRecording() {
        audioRecorder?.stop()
        isRecording = false
        loadRecordings()
    }

    func pauseRecording() {
        audioRecorder?.pause()
    }

    func resumeRecording() {
        audioRecorder?.record()
    }

    func loadRecordings() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]

        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: documentsPath, includingPropertiesForKeys: [.creationDateKey])
            recordings = fileURLs.compactMap { url in
                guard url.pathExtension == "m4a" else { return nil }

                let asset = AVAsset(url: url)
                let duration = CMTimeGetSeconds(asset.duration)

                // 获取文件创建时间
                let resourceValues = try? url.resourceValues(forKeys: [.creationDateKey])
                let createdAt = resourceValues?.creationDate ?? Date()

                return Recording(
                    url: url,
                    name: url.deletingPathExtension().lastPathComponent,
                    duration: duration.isNaN ? 0 : duration,
                    createdAt: createdAt
                )
            }.sorted { $0.createdAt > $1.createdAt }
        } catch {
            print("加载录音失败: \(error)")
            recordings = []
        }
    }

    func playRecording(_ recording: Recording) {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: recording.url)
            audioPlayer?.play()
        } catch {
            print("播放录音失败: \(error)")
        }
    }

    func stopPlayback() {
        audioPlayer?.stop()
    }

    func deleteRecording(_ recording: Recording) {
        do {
            try FileManager.default.removeItem(at: recording.url)
            loadRecordings()
        } catch {
            print("删除录音失败: \(error)")
        }
    }
}

// 真实的短信读取器类
class SMSReader: ObservableObject {
    @Published var messages: [SMSMessage] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var unreadCount = 0

    func loadMessages() {
        isLoading = true
        errorMessage = nil

        // 在实际应用中，这里应该通过以下方式获取短信：
        // 1. 通过WatchConnectivity从iPhone获取
        // 2. 通过MessageKit读取系统短信（需要权限）
        // 3. 通过自定义后端API

        DispatchQueue.global(qos: .background).async {
            // 模拟加载延迟
            Thread.sleep(forTimeInterval: 1.0)

            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "watchOS不支持直接读取系统短信，需要通过iPhone应用获取数据"
                self.messages = []
                self.unreadCount = 0
            }
        }
    }

    func markAsRead(_ message: SMSMessage) {
        if let index = messages.firstIndex(where: { $0.id == message.id }) {
            messages[index].isRead = true
            updateUnreadCount()
        }
    }

    private func updateUnreadCount() {
        unreadCount = messages.filter { !$0.isRead }.count
    }
}

#Preview {
    ContentView()
}
