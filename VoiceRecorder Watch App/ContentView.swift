import SwiftUI
import WatchKit
import AVFoundation

struct ContentView: View {
    @State private var isRecording = false
    @State private var isPaused = false
    @State private var recordingTime = "00:00"
    @State private var smsCount = 3
    @State private var showingRecordingsList = false
    @State private var showingSMSList = false
    @State private var recordingSeconds = 0
    @State private var timer: Timer?
    
    var body: some View {
        VStack(spacing: 8) {
            // 时间显示
            Text(recordingTime)
                .font(.title2)
                .foregroundColor(.white)
                .monospacedDigit()
            
            // 录音控制按钮
            HStack(spacing: 15) {
                // 录音/停止按钮
                Button(action: {
                    if isRecording {
                        stopRecording()
                    } else {
                        startRecording()
                    }
                }) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "record.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(isRecording ? .red : .green)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 暂停按钮（仅在录音时显示）
                if isRecording {
                    Button(action: {
                        pauseRecording()
                    }) {
                        Image(systemName: isPaused ? "play.circle.fill" : "pause.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.orange)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 功能按钮
            HStack(spacing: 20) {
                // 录音列表按钮
                Button(action: {
                    showingRecordingsList = true
                }) {
                    VStack(spacing: 2) {
                        Image(systemName: "list.bullet")
                            .font(.title3)
                        Text("录音")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.blue)
                
                // 短信按钮
                Button(action: {
                    showingSMSList = true
                }) {
                    VStack(spacing: 2) {
                        ZStack {
                            Image(systemName: "message.fill")
                                .font(.title3)
                            if smsCount > 0 {
                                Text("\(smsCount)")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(width: 16, height: 16)
                                    .background(Circle().fill(Color.red))
                                    .offset(x: 8, y: -8)
                            }
                        }
                        Text("短信")
                            .font(.caption2)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.green)
            }
        }
        .padding()
        .sheet(isPresented: $showingRecordingsList) {
            RecordingsListView()
        }
        .sheet(isPresented: $showingSMSList) {
            SMSListView(smsCount: $smsCount)
        }
    }
    
    func startRecording() {
        isRecording = true
        isPaused = false
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.start)
        
        // 开始计时
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if !isPaused {
                recordingSeconds += 1
                updateRecordingTime()
            }
        }
    }
    
    func stopRecording() {
        isRecording = false
        isPaused = false
        timer?.invalidate()
        timer = nil
        recordingTime = "00:00"
        recordingSeconds = 0
        WKInterfaceDevice.current().play(.stop)
    }
    
    func pauseRecording() {
        isPaused.toggle()
        WKInterfaceDevice.current().play(.click)
    }
    
    func updateRecordingTime() {
        let minutes = recordingSeconds / 60
        let seconds = recordingSeconds % 60
        recordingTime = String(format: "%02d:%02d", minutes, seconds)
    }
}

struct RecordingsListView: View {
    @Environment(\.dismiss) private var dismiss
    
    let recordings = [
        "录音 001 - 2分30秒",
        "录音 002 - 1分15秒", 
        "录音 003 - 3分45秒"
    ]
    
    var body: some View {
        NavigationView {
            List(recordings, id: \.self) { recording in
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(.blue)
                    Text(recording)
                        .font(.caption)
                }
            }
            .navigationTitle("录音列表")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct SMSListView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var smsCount: Int
    
    let messages = [
        SMSMessage(sender: "张三", content: "会议推迟到下午3点", time: "10:30"),
        SMSMessage(sender: "李四", content: "项目文档已发送", time: "09:15"),
        SMSMessage(sender: "王五", content: "明天的培训取消了", time: "昨天")
    ]
    
    var body: some View {
        NavigationView {
            List(messages) { message in
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(message.sender)
                            .font(.caption)
                            .fontWeight(.semibold)
                        Spacer()
                        Text(message.time)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    Text(message.content)
                        .font(.caption2)
                        .lineLimit(2)
                }
                .onTapGesture {
                    if smsCount > 0 {
                        smsCount -= 1
                    }
                }
            }
            .navigationTitle("短信")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            smsCount = 0 // 标记为已读
        }
    }
}

struct SMSMessage: Identifiable {
    let id = UUID()
    let sender: String
    let content: String
    let time: String
}

#Preview {
    ContentView()
}
