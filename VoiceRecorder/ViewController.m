//
//  ViewController.m
//  VoiceRecorder
//
//  Created by AI Assistant on 2025-06-21.
//

#import "ViewController.h"

@interface ViewController ()

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    
    // Add a label to indicate this is the companion app
    UILabel *label = [[UILabel alloc] init];
    label.text = @"VoiceRecorder\nCompanion App\n\nUse the Apple Watch app\nto record audio";
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [UIFont systemFontOfSize:18];
    label.textColor = [UIColor labelColor];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    
    [self.view addSubview:label];
    
    // Center the label
    [NSLayoutConstraint activateConstraints:@[
        [label.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [label.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor]
    ]];
}

@end
