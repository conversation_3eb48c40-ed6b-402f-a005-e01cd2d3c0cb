---
path:            '/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/26.0/CoreVideo.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1749170882000000000
    path:            '/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/26.0/CoreVideo.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            174752
  - mtime:           1747985744000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2215853
    sdk_relative:    true
  - mtime:           1747988041000000000
    path:            'usr/include/_DarwinFoundation2.apinotes'
    size:            1145
    sdk_relative:    true
  - mtime:           1747991281000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1747981195000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1747988076000000000
    path:            'usr/lib/swift/_DarwinFoundation1.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18871
    sdk_relative:    true
  - mtime:           1747988080000000000
    path:            'usr/lib/swift/_DarwinFoundation2.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2690
    sdk_relative:    true
  - mtime:           1747988080000000000
    path:            'usr/lib/swift/_DarwinFoundation3.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1635
    sdk_relative:    true
  - mtime:           1747987170000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4362
    sdk_relative:    true
  - mtime:           1747988093000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18803
    sdk_relative:    true
  - mtime:           1747989206000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            324116
    sdk_relative:    true
  - mtime:           1747989310000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            24508
    sdk_relative:    true
  - mtime:           1747621642000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            175563
    sdk_relative:    true
  - mtime:           1747990204000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            6738
    sdk_relative:    true
  - mtime:           1747621944000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            60333
    sdk_relative:    true
  - mtime:           1747990494000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22949
    sdk_relative:    true
  - mtime:           1748835900000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            54169
    sdk_relative:    true
  - mtime:           1747622510000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3688
    sdk_relative:    true
  - mtime:           1748656391000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1748658375000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1748835678000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1747622214000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            48385
    sdk_relative:    true
  - mtime:           1747989217000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3596
    sdk_relative:    true
  - mtime:           1747621636000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            97840
    sdk_relative:    true
  - mtime:           1748073611000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1135738
    sdk_relative:    true
  - mtime:           1748584075000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            98115
    sdk_relative:    true
  - mtime:           1747994214000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1748836901000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1748214757000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            103490
    sdk_relative:    true
  - mtime:           1748412439000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            42900
    sdk_relative:    true
  - mtime:           1747994245000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1747993697000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21758
    sdk_relative:    true
  - mtime:           1747994297000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            5513
    sdk_relative:    true
  - mtime:           1747994555000000000
    path:            'System/Library/Frameworks/CoreVideo.framework/Modules/CoreVideo.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            35469
    sdk_relative:    true
version:         1
...
