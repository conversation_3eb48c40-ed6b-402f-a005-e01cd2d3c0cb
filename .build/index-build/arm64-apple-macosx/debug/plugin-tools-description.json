{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module": {"executable": "/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources", "importPath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift"}, {"kind": "file", "name": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt"}, {"kind": "file", "name": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources"}], "isLibrary": false, "moduleName": "VoiceRecorderWatch", "moduleOutputPath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules/VoiceRecorderWatch.swiftmodule", "objects": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/VoiceRecorderWatchApp.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-incremental", "-enable-batch-mode", "-serialize-diagnostics", "-index-store-path", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-DSWIFT_MODULE_RESOURCE_BUNDLE_UNAVAILABLE", "-module-cache-path", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-plugin-path", "/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing", "-sdk", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "ai_metting_watch"], "outputFileMapPath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules/VoiceRecorderWatch.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift"], "tempsPath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"VoiceRecorderWatch": ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "VoiceRecorderWatch", "-package-name", "ai_metting_watch", "-c", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift", "-I", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-incremental", "-enable-batch-mode", "-serialize-diagnostics", "-index-store-path", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-DSWIFT_MODULE_RESOURCE_BUNDLE_UNAVAILABLE", "-module-cache-path", "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-plugin-path", "/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing", "-sdk", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "ai_metting_watch", "-driver-use-frontend-path", "/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"VoiceRecorderWatch": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"default": {}}, "writeCommands": {"/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift"}], "outputFilePath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources"}, "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt"}}}