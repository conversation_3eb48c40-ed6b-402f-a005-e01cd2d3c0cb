client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module": ["<VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module>"]
  "main": ["<VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module>"]
  "test": ["<VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift"]
    outputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources"

  "/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt"

  "<VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules/VoiceRecorderWatch.swiftmodule"]
    outputs: ["<VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module>"]

  "C.VoiceRecorderWatch-arm64-apple-macosx26.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/VoiceRecorderWatchApp.swift","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/swift-version-39B54973F684ADAB.txt","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources"]
    outputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules/VoiceRecorderWatch.swiftmodule"]
    description: "Compiling Swift Module 'VoiceRecorderWatch' (1 sources)"
    args: ["/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","VoiceRecorderWatch","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules/VoiceRecorderWatch.swiftmodule","-output-file-map","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/output-file-map.json","-incremental","@/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/VoiceRecorderWatch.build/sources","-I","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-incremental","-enable-batch-mode","-serialize-diagnostics","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-DSWIFT_MODULE_RESOURCE_BUNDLE_UNAVAILABLE","-module-cache-path","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","VoiceRecorderWatch_main","-parse-as-library","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-plugin-path","/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing","-sdk","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk","-F","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk","-Xcc","-F","-Xcc","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","ai_metting_watch"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/Package.swift","/Users/<USER>/Documents/workspace/ai/apple/AI-Metting-watch/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

